"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  XMarkIcon,
  ExclamationTriangleIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import * as Dialog from "@radix-ui/react-dialog";
import { Case } from "../../_lib/types";

interface DeleteCaseModalProps {
  case: Case;
  isOpen: boolean;
  onClose: () => void;
}

export function DeleteCaseModal({ case: caseData, isOpen, onClose }: DeleteCaseModalProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [confirmText, setConfirmText] = useState("");
  const router = useRouter();

  const handleDelete = async () => {
    if (confirmText !== caseData.title) {
      return;
    }

    setIsDeleting(true);

    try {
      // Simular eliminación del caso
      // En una implementación real, aquí harías la llamada a la API
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Redirigir al dashboard después de eliminar
      router.push("/dashboard");
      
      // Mostrar notificación de éxito (opcional)
      console.log(`Caso "${caseData.title}" eliminado exitosamente`);
      
    } catch (error) {
      console.error("Error al eliminar el caso:", error);
      setIsDeleting(false);
    }
  };

  const isConfirmValid = confirmText === caseData.title;

  return (
    <Dialog.Root open={isOpen} onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 dark:bg-black/70 z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-50 w-full max-w-md p-6">
          <div className="flex items-center justify-between mb-6">
            <Dialog.Title className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
              <ExclamationTriangleIcon className="h-6 w-6 text-red-500 mr-2" />
              Eliminar Caso
            </Dialog.Title>
            <Dialog.Close asChild>
              <button className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer">
                <XMarkIcon className="h-5 w-5" />
              </button>
            </Dialog.Close>
          </div>

          <Dialog.Description className="sr-only">
            Confirmación para eliminar el caso permanentemente
          </Dialog.Description>

          <div className="space-y-4">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <div className="flex items-start">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-500 dark:text-red-400 mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-red-800 dark:text-red-200 mb-1">
                    ¡Advertencia! Esta acción no se puede deshacer
                  </h4>
                  <p className="text-sm text-red-700 dark:text-red-300">
                    Eliminarás permanentemente el caso <strong>&ldquo;{caseData.title}&rdquo;</strong> y todos sus datos asociados:
                  </p>
                  <ul className="text-sm text-red-700 dark:text-red-300 mt-2 ml-4 list-disc">
                    <li>Mensajes y conversaciones</li>
                    <li>Documentos subidos</li>
                    <li>Tareas y milestones</li>
                    <li>Historial de actividades</li>
                  </ul>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Para confirmar, escribe el nombre del caso:
              </label>
              <input
                type="text"
                value={confirmText}
                onChange={(e) => setConfirmText(e.target.value)}
                placeholder={caseData.title}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              />
            </div>

            <div className="flex space-x-3 pt-4">
              <button
                onClick={onClose}
                disabled={isDeleting}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancelar
              </button>
              <button
                onClick={handleDelete}
                disabled={!isConfirmValid || isDeleting}
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors cursor-pointer"
              >
                {isDeleting ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Eliminando...
                  </div>
                ) : (
                  <div className="flex items-center justify-center">
                    <TrashIcon className="h-4 w-4 mr-2" />
                    Eliminar Caso
                  </div>
                )}
              </button>
            </div>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
