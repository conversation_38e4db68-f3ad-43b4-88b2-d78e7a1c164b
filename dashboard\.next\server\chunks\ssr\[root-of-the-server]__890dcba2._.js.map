{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/CaseDetailPage.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CaseDetailPage = registerClientReference(\n    function() { throw new Error(\"Attempted to call CaseDetailPage() from the server but CaseDetailPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/_components/cases/CaseDetailPage.tsx <module evaluation>\",\n    \"CaseDetailPage\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0EACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/CaseDetailPage.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CaseDetailPage = registerClientReference(\n    function() { throw new Error(\"Attempted to call CaseDetailPage() from the server but CaseDetailPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/_components/cases/CaseDetailPage.tsx\",\n    \"CaseDetailPage\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,sDACA", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/CaseDetailWrapper.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CaseDetailWrapper = registerClientReference(\n    function() { throw new Error(\"Attempted to call CaseDetailWrapper() from the server but CaseDetailWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/_components/cases/CaseDetailWrapper.tsx <module evaluation>\",\n    \"CaseDetailWrapper\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,6EACA", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/cases/CaseDetailWrapper.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CaseDetailWrapper = registerClientReference(\n    function() { throw new Error(\"Attempted to call CaseDetailWrapper() from the server but CaseDetailWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/_components/cases/CaseDetailWrapper.tsx\",\n    \"CaseDetailWrapper\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,yDACA", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/dashboard/%5BcaseId%5D/page.tsx"], "sourcesContent": ["import { notFound } from \"next/navigation\";\r\nimport { readFile } from \"fs/promises\";\r\nimport { join } from \"path\";\r\nimport { CaseDetailPage } from \"../../_components/cases/CaseDetailPage\";\r\nimport { CaseDetailWrapper } from \"../../_components/cases/CaseDetailWrapper\";\r\nimport { Case } from \"../../_lib/types\";\r\n\r\nasync function getCase(caseId: string): Promise<Case | null> {\r\n  try {\r\n    // Read the JSON file directly from the filesystem\r\n    // This works reliably in both development and production\r\n    const filePath = join(process.cwd(), \"public\", \"data\", \"cases.json\");\r\n    const fileContents = await readFile(filePath, \"utf8\");\r\n    const cases: Case[] = JSON.parse(fileContents);\r\n\r\n    // Try to find the case in the static JSON data\r\n    const staticCase = cases.find((c) => c.id === caseId);\r\n    return staticCase || null;\r\n  } catch (error) {\r\n    console.error(\"Error fetching case:\", error);\r\n    // For errors (file read, JSO<PERSON> parse), we should call notFound\r\n    notFound();\r\n  }\r\n}\r\n\r\ninterface CaseDetailProps {\r\n  params: Promise<{\r\n    caseId: string;\r\n  }>;\r\n}\r\n\r\nexport default async function CaseDetail({ params }: CaseDetailProps) {\r\n  const { caseId } = await params;\r\n  const staticCase = await getCase(caseId);\r\n\r\n  // If we found the case in static data, render it directly\r\n  if (staticCase) {\r\n    return <CaseDetailPage case={staticCase} />;\r\n  }\r\n\r\n  // If not found in static data, use the wrapper to check localStorage\r\n  return <CaseDetailWrapper caseId={caseId} />;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAGA,eAAe,QAAQ,MAAc;IACnC,IAAI;QACF,kDAAkD;QAClD,yDAAyD;QACzD,MAAM,WAAW,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,IAAI,UAAU,QAAQ;QACvD,MAAM,eAAe,MAAM,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,EAAE,UAAU;QAC9C,MAAM,QAAgB,KAAK,KAAK,CAAC;QAEjC,+CAA+C;QAC/C,MAAM,aAAa,MAAM,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;QAC9C,OAAO,cAAc;IACvB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,8DAA8D;QAC9D,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;AACF;AAQe,eAAe,WAAW,EAAE,MAAM,EAAmB;IAClE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,aAAa,MAAM,QAAQ;IAEjC,0DAA0D;IAC1D,IAAI,YAAY;QACd,qBAAO,8OAAC,8IAAA,CAAA,iBAAc;YAAC,MAAM;;;;;;IAC/B;IAEA,qEAAqE;IACrE,qBAAO,8OAAC,iJAAA,CAAA,oBAAiB;QAAC,QAAQ;;;;;;AACpC", "debugId": null}}]}