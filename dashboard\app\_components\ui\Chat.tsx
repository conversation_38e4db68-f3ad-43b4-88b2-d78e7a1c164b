"use client";

import { useState, useRef, useEffect } from "react";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import {
  PaperAirplaneIcon,
  PaperClipIcon,
  MagnifyingGlassIcon,
  CheckIcon,
  CheckCircleIcon,
} from "@heroicons/react/24/outline";
import { Message } from "../../_lib/types";

interface ChatProps {
  messages: Message[];
  caseId: string;
}

const quickReplies = [
  "Perfecto, revisaré la documentación.",
  "Necesito más información sobre este punto.",
  "Procederemos con la siguiente etapa.",
  "Te mantendré informado del progreso.",
];

export function Chat({ messages: initialMessages, caseId }: ChatProps) {
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [newMessage, setNewMessage] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [showQuickReplies, setShowQuickReplies] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const filteredMessages = messages.filter(
    (message) =>
      searchTerm === "" ||
      message.content.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim()) return;

    const message: Message = {
      id: `msg-${crypto.randomUUID()}`,
      sender: "lawyer",
      content: newMessage.trim(),
      timestamp: new Date().toISOString(),
      status: "sent",
      attachments: [],
    };

    setMessages((prev) => [...prev, message]);
    setNewMessage("");
    setShowQuickReplies(false);

    // Simulate message status updates
    setTimeout(() => {
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === message.id ? { ...msg, status: "delivered" } : msg
        )
      );
    }, 1000);

    setTimeout(() => {
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === message.id ? { ...msg, status: "read" } : msg
        )
      );
    }, 3000);

    // Save to sessionStorage
    const savedMessages = sessionStorage.getItem(`chat-${caseId}`);
    const allMessages = savedMessages
      ? JSON.parse(savedMessages)
      : initialMessages;
    sessionStorage.setItem(
      `chat-${caseId}`,
      JSON.stringify([...allMessages, message])
    );
  };

  const handleQuickReply = (reply: string) => {
    setNewMessage(reply);
    setShowQuickReplies(false);
  };

  const handleFileAttach = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      // In a real app, you would upload the file and get a URL
      const file = files[0];
      const message: Message = {
        id: `msg-${Date.now()}`,
        sender: "lawyer",
        content: `Archivo adjunto: ${file.name}`,
        timestamp: new Date().toISOString(),
        status: "sent",
        attachments: [
          {
            id: `att-${Date.now()}`,
            name: file.name,
            type: file.type,
            size: `${Math.round(file.size / 1024)}KB`,
          },
        ],
      };

      setMessages((prev) => [...prev, message]);
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case "sent":
        return <CheckIcon className="h-3 w-3" />;
      case "delivered":
        return <CheckIcon className="h-3 w-3" />;
      case "read":
        return <CheckCircleIcon className="h-3 w-3" />;
      default:
        return null;
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-96 flex flex-col">
      {/* Search Bar */}
      <div className="border-b border-gray-200 dark:border-gray-700 p-3">
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Buscar en el chat..."
            className="w-full pl-10 pr-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {filteredMessages.map((message) => (
          <div
            key={message.id}
            className={`flex ${
              message.sender === "lawyer" ? "justify-end" : "justify-start"
            }`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.sender === "lawyer"
                  ? "bg-blue-600 text-white"
                  : "bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              }`}
            >
              <p className="text-sm">{message.content}</p>

              {/* Attachments */}
              {message.attachments && message.attachments.length > 0 && (
                <div className="mt-2 space-y-1">
                  {message.attachments.map((attachment) => (
                    <div
                      key={attachment.id}
                      className={`flex items-center space-x-2 p-2 rounded ${
                        message.sender === "lawyer"
                          ? "bg-blue-500"
                          : "bg-gray-200 dark:bg-gray-600"
                      }`}
                    >
                      <PaperClipIcon className="h-3 w-3" />
                      <span className="text-xs">{attachment.name}</span>
                      <span className="text-xs opacity-75">
                        ({attachment.size})
                      </span>
                    </div>
                  ))}
                </div>
              )}

              <div className="flex items-center justify-between mt-1">
                <p
                  className={`text-xs ${
                    message.sender === "lawyer"
                      ? "text-blue-100"
                      : "text-gray-500 dark:text-gray-400"
                  }`}
                >
                  {format(new Date(message.timestamp), "HH:mm", { locale: es })}
                </p>
                {message.sender === "lawyer" && (
                  <div
                    className={`text-xs ${
                      message.status === "read"
                        ? "text-blue-200"
                        : "text-blue-300"
                    }`}
                  >
                    {getStatusIcon(message.status)}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Quick Replies */}
      {showQuickReplies && (
        <div className="border-t border-gray-200 dark:border-gray-700 p-3">
          <div className="flex flex-wrap gap-2">
            {quickReplies.map((reply, index) => (
              <button
                key={index}
                onClick={() => handleQuickReply(reply)}
                className="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                {reply}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Message Input */}
      <form
        onSubmit={handleSendMessage}
        className="border-t border-gray-200 dark:border-gray-700 p-4"
      >
        <div className="flex space-x-2">
          <button
            type="button"
            onClick={handleFileAttach}
            className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-400 p-2"
          >
            <PaperClipIcon className="h-4 w-4" />
          </button>
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onFocus={() => setShowQuickReplies(true)}
            placeholder="Escribe un mensaje..."
            className="flex-1 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <button
            type="submit"
            disabled={!newMessage.trim()}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <PaperAirplaneIcon className="h-4 w-4" />
          </button>
        </div>
        <input
          ref={fileInputRef}
          type="file"
          onChange={handleFileSelect}
          className="hidden"
          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
        />
      </form>
    </div>
  );
}
