import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./_components/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  // Note: darkMode and theme.colors moved to CSS-first config in globals.css
  // This ensures compatibility with Tailwind v4
  plugins: [require("@tailwindcss/typography")],
};
export default config;
