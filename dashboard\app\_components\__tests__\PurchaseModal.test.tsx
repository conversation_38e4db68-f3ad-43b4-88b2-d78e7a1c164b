import { describe, it, expect, beforeEach, vi } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { PurchaseModal } from "../modals/PurchaseModal";
import { ThemeProvider } from "../../_contexts/ThemeContext";
import { Template } from "../../_lib/types";

// Mock framer-motion
vi.mock("framer-motion", () => ({
  motion: {
    div: ({ children, ...props }: React.ComponentProps<"div">) => (
      <div {...props}>{children}</div>
    ),
  },
}));

// Mock matchMedia for theme detection
const mockMatchMedia = vi.fn().mockImplementation((query: string) => ({
  matches: query === "(prefers-color-scheme: dark)",
  media: query,
  onchange: null,
  addListener: vi.fn(),
  removeListener: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
}));

window.matchMedia = mockMatchMedia;

const mockTemplate: Template = {
  id: "1",
  title: "Contrato de Trabajo",
  description: "Plantilla completa para contratos laborales",
  price: 2500,
  legalArea: "Laboral",
  documentType: "Contrato",
  tags: ["trabajo", "laboral"],
  rating: 4.5,
  downloadCount: 100,
  author: "Test Author",
  authorId: "author-1",
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  preview: "Preview content",
  fileUrl: "/templates/contrato-trabajo.pdf",
  featured: false,
  status: "published",
};

interface TestAppProps {
  template?: Template;
  onClose?: () => void;
  onPurchaseComplete?: () => void;
  initialTheme?: "light" | "dark" | "system";
}

function TestApp({
  template = mockTemplate,
  onClose = vi.fn(),
  onPurchaseComplete = vi.fn(),
  initialTheme = "light",
}: TestAppProps) {
  // Set initial theme in localStorage before rendering
  if (typeof window !== "undefined") {
    localStorage.setItem("theme", initialTheme);
  }

  return (
    <ThemeProvider>
      <PurchaseModal
        template={template}
        onClose={onClose}
        onPurchaseComplete={onPurchaseComplete}
      />
    </ThemeProvider>
  );
}

describe("PurchaseModal", () => {
  beforeEach(() => {
    localStorage.clear();
    document.documentElement.className = "";
    vi.clearAllMocks();
  });

  describe("Basic Functionality", () => {
    it("should render the purchase modal with details step", () => {
      render(<TestApp />);

      expect(screen.getByText("Detalles de la Plantilla")).toBeInTheDocument();
      expect(screen.getByText("Contrato de Trabajo")).toBeInTheDocument();
      expect(
        screen.getByText("Plantilla completa para contratos laborales")
      ).toBeInTheDocument();
      expect(screen.getByText("ARS 2.500")).toBeInTheDocument();
      expect(
        screen.getByText("Lo que incluye esta plantilla:")
      ).toBeInTheDocument();
    });

    it("should call onClose when close button is clicked", async () => {
      const onClose = vi.fn();
      const user = userEvent.setup();

      render(<TestApp onClose={onClose} />);

      const closeButton = screen.getByRole("button", { name: "" });
      await user.click(closeButton);

      expect(onClose).toHaveBeenCalledOnce();
    });

    it("should navigate to payment step when continue button is clicked", async () => {
      const user = userEvent.setup();

      render(<TestApp />);

      const continueButton = screen.getByText("Continuar");
      await user.click(continueButton);

      expect(screen.getAllByText("Método de Pago")).toHaveLength(2); // Dialog title and content title
      expect(screen.getByText("Tarjeta de Crédito/Débito")).toBeInTheDocument();
    });
  });

  describe("Dark Theme Support", () => {
    it("should apply dark theme classes to modal content when dark mode is active", async () => {
      // Set up dark theme
      mockMatchMedia.mockImplementation((query: string) => ({
        matches: query === "(prefers-color-scheme: dark)",
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));

      render(<TestApp initialTheme="dark" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      // Check modal content has dark theme classes
      const modalContent = screen
        .getByText("Detalles de la Plantilla")
        .closest('[role="dialog"]');
      expect(modalContent).toHaveClass("dark:bg-gray-800");
    });

    it("should apply dark theme classes to title and text elements", async () => {
      render(<TestApp initialTheme="dark" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      // Check title has dark theme classes
      const title = screen.getByText("Detalles de la Plantilla");
      expect(title).toHaveClass("dark:text-gray-100");

      // Check template title has dark theme classes
      const templateTitle = screen.getByText("Contrato de Trabajo");
      expect(templateTitle).toHaveClass("dark:text-gray-100");

      // Check description has dark theme classes
      const description = screen.getByText(
        "Plantilla completa para contratos laborales"
      );
      expect(description).toHaveClass("dark:text-gray-400");
    });

    it("should apply dark theme classes to price display", async () => {
      render(<TestApp initialTheme="dark" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      // Check price display has dark theme classes
      const priceContainer = screen.getByText("ARS 2.500").closest("div");
      expect(priceContainer).toHaveClass("dark:text-blue-400");
    });

    it("should apply dark theme classes to includes section", async () => {
      render(<TestApp initialTheme="dark" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      // Check includes section has dark theme classes
      const includesSection = screen
        .getByText("Lo que incluye esta plantilla:")
        .closest("div");
      expect(includesSection).toHaveClass("dark:bg-gray-700");

      const includesTitle = screen.getByText("Lo que incluye esta plantilla:");
      expect(includesTitle).toHaveClass("dark:text-gray-100");
    });

    it("should apply dark theme classes to buttons", async () => {
      render(<TestApp initialTheme="dark" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      // Check cancel button has dark theme classes
      const cancelButton = screen.getByText("Cancelar");
      expect(cancelButton).toHaveClass("dark:text-gray-300");
      expect(cancelButton).toHaveClass("dark:bg-gray-700");
      expect(cancelButton).toHaveClass("dark:border-gray-600");
      expect(cancelButton).toHaveClass("dark:hover:bg-gray-600");

      // Check continue button has dark theme classes (should remain blue but with dark focus ring offset)
      const continueButton = screen.getByText("Continuar");
      expect(continueButton).toHaveClass("dark:focus:ring-offset-gray-800");
    });

    it("should apply dark theme classes to close button", async () => {
      render(<TestApp initialTheme="dark" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      // Check close button has dark theme classes
      const closeButton = screen.getByRole("button", { name: "" });
      expect(closeButton).toHaveClass("dark:text-gray-500");
      expect(closeButton).toHaveClass("dark:hover:text-gray-300");
    });

    it("should apply dark theme classes to payment step elements", async () => {
      const user = userEvent.setup();

      render(<TestApp initialTheme="dark" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(true);
      });

      // Navigate to payment step
      const continueButton = screen.getByText("Continuar");
      await user.click(continueButton);

      // Check payment method options have dark theme classes
      const creditCardOption = screen
        .getByText("Tarjeta de Crédito/Débito")
        .closest("label");
      expect(creditCardOption).toHaveClass("dark:border-gray-600");
      expect(creditCardOption).toHaveClass("dark:hover:bg-gray-700");

      const creditCardText = screen.getByText("Tarjeta de Crédito/Débito");
      expect(creditCardText).toHaveClass("dark:text-gray-100");
    });

    it("should maintain light theme classes when light mode is active", async () => {
      // Explicitly set light theme and reset dark class
      localStorage.setItem("theme", "light");
      document.documentElement.classList.remove("dark");

      // Mock system preference for light mode
      mockMatchMedia.mockImplementation((query: string) => ({
        matches: false, // System prefers light
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));

      render(<TestApp initialTheme="light" />);

      await waitFor(() => {
        expect(document.documentElement.classList.contains("dark")).toBe(false);
      });

      // Check modal content has light theme classes
      const modalContent = screen
        .getByText("Detalles de la Plantilla")
        .closest('[role="dialog"]');
      expect(modalContent).toHaveClass("bg-white");
    });
  });
});
