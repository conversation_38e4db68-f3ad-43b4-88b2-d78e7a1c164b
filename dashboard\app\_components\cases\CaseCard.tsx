"use client";

import Link from "next/link";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import {
  ClockIcon,
  UserIcon,
  ScaleIcon,
  ChatBubbleLeftRightIcon,
} from "@heroicons/react/24/outline";
import { Case } from "../../_lib/types";

interface CaseCardProps {
  case: Case;
}

const priorityColors = {
  low: "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200",
  medium:
    "bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200",
  high: "bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200",
};

const typeColors = {
  Laboral: "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200",
  Civil:
    "bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200",
  Familia: "bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200",
  Penal: "bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200",
  Comercial:
    "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200",
};

export function CaseCard({
  case: caseData,
}: CaseCardProps) {
  const formattedDate = format(
    new Date(caseData.createdAt),
    "dd MMM yyyy - HH:mm",
    {
      locale: es,
    }
  );

  // Verificar si es un caso creado por el usuario
  const isUserCreatedCase = () => {
    // Los casos creados por usuario tienen IDs con formato: c-xxxxxxxx (8 caracteres aleatorios)
    // Los casos del JSON tienen IDs como: c-001, c-002, etc.
    const idPattern = /^c-[a-f0-9]{8}$/i;
    return idPattern.test(caseData.id);
  };

  return (
    <div className="group relative bg-white dark:bg-gray-800 border-0 rounded-xl p-5 shadow-sm hover:shadow-lg transition-all duration-200 overflow-hidden">
      {/* Gradient border effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200" />

      {/* Left border accent */}
      <div className={`absolute left-0 top-0 bottom-0 w-1 rounded-l-xl ${
        caseData.priority === "high"
          ? "bg-gradient-to-b from-red-500 to-red-600"
          : caseData.priority === "medium"
          ? "bg-gradient-to-b from-yellow-500 to-orange-500"
          : "bg-gradient-to-b from-green-500 to-emerald-500"
      }`} />

      <Link href={`/dashboard/${caseData.id}`} className="block relative z-10">
        <div className="space-y-4">
          {/* Header with priority and notifications */}
          <div className="flex items-start justify-between">
            <div className="flex-1 pr-3">
              <h4 className="font-semibold text-gray-900 dark:text-gray-100 text-sm leading-tight line-clamp-2 mb-2">
                {caseData.title}
              </h4>

              {/* Type badge */}
              <span
                className={`inline-flex items-center text-xs font-medium px-2.5 py-1 rounded-lg ${
                  typeColors[caseData.type as keyof typeof typeColors] ||
                  "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                }`}
              >
                {caseData.type}
              </span>
            </div>

            <div className="flex flex-col items-end space-y-2">
              {/* Priority badge */}
              <span
                className={`text-xs font-semibold px-2.5 py-1 rounded-lg shadow-sm ${
                  priorityColors[caseData.priority]
                }`}
              >
                {caseData.priority === "high"
                  ? "Alta"
                  : caseData.priority === "medium"
                  ? "Media"
                  : "Baja"}
              </span>

              {/* Unread Messages Badge - oculto para casos creados por usuario */}
              {!isUserCreatedCase() && caseData.unreadMessagesCount && caseData.unreadMessagesCount > 0 && (
                <div className="relative">
                  <div className="bg-red-500 dark:bg-red-600 text-white text-xs font-bold rounded-full h-6 w-6 flex items-center justify-center shadow-lg">
                    {caseData.unreadMessagesCount > 9 ? "9+" : caseData.unreadMessagesCount}
                  </div>
                  <div className="absolute -top-1 -right-1 h-3 w-3 bg-red-400 rounded-full animate-ping" />
                </div>
              )}
            </div>
          </div>

          {/* Client info */}
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-700/50 rounded-lg px-3 py-2">
            <UserIcon className="h-4 w-4 mr-2 text-gray-400 dark:text-gray-500" />
            <span className="font-medium">{caseData.client}</span>
          </div>



          {/* Footer with metadata */}
          <div className="flex items-center justify-between pt-2 border-t border-gray-100 dark:border-gray-700">
            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
              <ClockIcon className="h-3 w-3 mr-1" />
              {formattedDate}
            </div>

            {caseData.similarCount > 0 && (
              <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md">
                <ScaleIcon className="h-3 w-3 mr-1" />
                {caseData.similarCount} similares
              </div>
            )}
          </div>

          {/* Messages indicator - oculto para casos creados por usuario */}
          {!isUserCreatedCase() && (() => {
            const messageCount = caseData.unreadMessagesCount || 0;
            const hasUnread = messageCount > 0;

            return (
              <div className={`flex items-center justify-center text-xs rounded-lg py-2 border ${
                hasUnread
                  ? "text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800"
                  : "text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800"
              }`}>
                <ChatBubbleLeftRightIcon className="h-3 w-3 mr-1" />
                <span className="font-medium">
                  {hasUnread
                    ? `${messageCount} mensaje${messageCount !== 1 ? "s" : ""} sin leer`
                    : "Sin mensajes pendientes"
                  }
                </span>
              </div>
            );
          })()}
        </div>
      </Link>
    </div>
  );
}
