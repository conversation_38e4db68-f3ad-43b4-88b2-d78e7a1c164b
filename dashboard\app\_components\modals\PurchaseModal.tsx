"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import {
  XMarkIcon,
  CreditCardIcon,
  CheckCircleIcon,
  CurrencyDollarIcon,
} from "@heroicons/react/24/outline";
import * as Dialog from "@radix-ui/react-dialog";
import { Template } from "../../_lib/types";

interface PurchaseModalProps {
  template: Template;
  onClose: () => void;
  onPurchaseComplete: () => void;
}

export function PurchaseModal({
  template,
  onClose,
  onPurchaseComplete,
}: PurchaseModalProps) {
  const [step, setStep] = useState<"details" | "payment" | "success">(
    "details"
  );
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState("credit_card");

  const handlePurchase = async () => {
    setIsProcessing(true);

    // Simulate payment processing
    setTimeout(() => {
      setIsProcessing(false);
      setStep("success");

      // Auto-close after success
      setTimeout(() => {
        onPurchaseComplete();
        onClose();
      }, 2000);
    }, 2000);
  };

  const renderStepContent = () => {
    switch (step) {
      case "details":
        return (
          <div className="space-y-6">
            <div className="text-center">
              <div className="text-6xl mb-4">📄</div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                {template.title}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                {template.description}
              </p>
              <div className="flex items-center justify-center space-x-2 text-2xl font-bold text-blue-600 dark:text-blue-400">
                <CurrencyDollarIcon className="h-6 w-6" />
                <span>ARS {template.price.toLocaleString("es-AR")}</span>
              </div>
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                Lo que incluye esta plantilla:
              </h4>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <li>• Documento en formato Word editable</li>
                <li>• Cláusulas actualizadas según legislación vigente</li>
                <li>• Guía de uso y personalización</li>
                <li>• Soporte técnico por 30 días</li>
                <li>• Actualizaciones gratuitas por 1 año</li>
              </ul>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer"
              >
                Cancelar
              </button>
              <button
                onClick={() => setStep("payment")}
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer"
              >
                Continuar
              </button>
            </div>
          </div>
        );

      case "payment":
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Método de Pago
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Total a pagar:{" "}
                <span className="font-semibold">
                  ARS {template.price.toLocaleString("es-AR")}
                </span>
              </p>
            </div>

            <div className="space-y-4">
              <div className="space-y-3">
                <label className="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                  <input
                    type="radio"
                    name="payment"
                    value="credit_card"
                    checked={paymentMethod === "credit_card"}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="text-blue-600 focus:ring-blue-500 cursor-pointer"
                  />
                  <CreditCardIcon className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    Tarjeta de Crédito/Débito
                  </span>
                </label>

                <label className="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                  <input
                    type="radio"
                    name="payment"
                    value="mercadopago"
                    checked={paymentMethod === "mercadopago"}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="text-blue-600 focus:ring-blue-500 cursor-pointer"
                  />
                  <div className="w-5 h-5 bg-blue-500 rounded"></div>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    MercadoPago
                  </span>
                </label>

                <label className="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                  <input
                    type="radio"
                    name="payment"
                    value="transfer"
                    checked={paymentMethod === "transfer"}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="text-blue-600 focus:ring-blue-500 cursor-pointer"
                  />
                  <div className="w-5 h-5 bg-green-500 rounded"></div>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    Transferencia Bancaria
                  </span>
                </label>
              </div>

              {paymentMethod === "credit_card" && (
                <div className="space-y-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <input
                    type="text"
                    placeholder="Número de tarjeta"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <div className="grid grid-cols-2 gap-3">
                    <input
                      type="text"
                      placeholder="MM/AA"
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <input
                      type="text"
                      placeholder="CVV"
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <input
                    type="text"
                    placeholder="Nombre del titular"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              )}
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setStep("details")}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer"
              >
                Volver
              </button>
              <button
                onClick={handlePurchase}
                disabled={isProcessing}
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors cursor-pointer"
              >
                {isProcessing ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Procesando...
                  </div>
                ) : (
                  "Confirmar Compra"
                )}
              </button>
            </div>
          </div>
        );

      case "success":
        return (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            className="text-center space-y-6"
          >
            <div className="text-green-600 dark:text-green-400">
              <CheckCircleIcon className="h-16 w-16 mx-auto mb-4" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                ¡Compra Exitosa!
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                La plantilla &quot;{template.title}&quot; ha sido agregada a tu
                biblioteca. Puedes descargarla desde la sección &quot;Mis
                Plantillas&quot;.
              </p>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <p className="text-sm text-green-800 dark:text-green-200">
                Se ha enviado el recibo de compra a tu email registrado.
              </p>
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog.Root open onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 dark:bg-black/70 z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-50 w-full max-w-md p-6">
          <div className="flex items-center justify-between mb-6">
            <Dialog.Title className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {step === "details" && "Detalles de la Plantilla"}
              {step === "payment" && "Método de Pago"}
              {step === "success" && "Compra Completada"}
            </Dialog.Title>
            {step !== "success" && (
              <Dialog.Close asChild>
                <button className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer">
                  <XMarkIcon className="h-5 w-5" />
                </button>
              </Dialog.Close>
            )}
          </div>

          <Dialog.Description className="sr-only">
            {step === "details" &&
              `Detalles de compra para la plantilla ${template.title}`}
            {step === "payment" &&
              "Formulario de pago para completar la compra"}
            {step === "success" && "Confirmación de compra exitosa"}
          </Dialog.Description>

          {renderStepContent()}
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
