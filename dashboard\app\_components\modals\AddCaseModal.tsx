"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import {
  XMarkIcon,
  CheckCircleIcon,
  PlusIcon,
  CalendarIcon,
  UserIcon,
  ScaleIcon,
} from "@heroicons/react/24/outline";
import * as Dialog from "@radix-ui/react-dialog";
import { Case } from "../../_lib/types";

interface AddCaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCaseAdded: (newCase: Case) => void;
}

interface FormData {
  title: string;
  type: string;
  client: string;
  description: string;
  priority: "low" | "medium" | "high";
  estimatedCost: string;
  milestones: {
    title: string;
    dueDate: string;
  }[];
}

const CASE_TYPES = [
  "Laboral",
  "Civil",
  "Familia",
  "Penal",
  "Comercial",
  "Administrativo",
  "Constitucional",
  "Tributario",
];

const PRIORITIES = [
  { value: "low", label: "Baja", color: "text-green-600 dark:text-green-400" },
  { value: "medium", label: "Media", color: "text-yellow-600 dark:text-yellow-400" },
  { value: "high", label: "Alta", color: "text-red-600 dark:text-red-400" },
];

export function AddCaseModal({ isOpen, onClose, onCaseAdded }: AddCaseModalProps) {
  const [step, setStep] = useState<"basic" | "details" | "milestones">("basic");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    title: "",
    type: "",
    client: "",
    description: "",
    priority: "medium",
    estimatedCost: "",
    milestones: [
      { title: "", dueDate: "" },
    ],
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateStep = (currentStep: string): boolean => {
    const newErrors: Record<string, string> = {};

    if (currentStep === "basic") {
      if (!formData.title.trim()) newErrors.title = "El título es obligatorio";
      if (!formData.type) newErrors.type = "El tipo de caso es obligatorio";
      if (!formData.client.trim()) newErrors.client = "El nombre del cliente es obligatorio";
    }

    if (currentStep === "details") {
      if (!formData.description.trim()) newErrors.description = "La descripción es obligatoria";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (!validateStep(step)) return;

    if (step === "basic") setStep("details");
    else if (step === "details") setStep("milestones");
    else if (step === "milestones") handleSubmit();
  };

  const handleBack = () => {
    if (step === "details") setStep("basic");
    else if (step === "milestones") setStep("details");
  };

  const addMilestone = () => {
    setFormData(prev => ({
      ...prev,
      milestones: [...prev.milestones, { title: "", dueDate: "" }],
    }));
  };

  const removeMilestone = (index: number) => {
    setFormData(prev => ({
      ...prev,
      milestones: prev.milestones.filter((_, i) => i !== index),
    }));
  };

  const updateMilestone = (index: number, field: "title" | "dueDate", value: string) => {
    setFormData(prev => ({
      ...prev,
      milestones: prev.milestones.map((milestone, i) =>
        i === index ? { ...milestone, [field]: value } : milestone
      ),
    }));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);

    try {
      // Crear el nuevo caso
      const newCase: Case = {
        id: `c-${crypto.randomUUID().slice(0, 8)}`,
        title: formData.title,
        type: formData.type,
        client: formData.client,
        status: "new",
        progress: 0,
        createdAt: new Date().toISOString(),
        similarCount: 0,
        description: formData.description,
        priority: formData.priority,
        estimatedCost: formData.estimatedCost || undefined,
        complexityScore: Math.floor(Math.random() * 10) + 1, // Simulado
        riskAssessment: formData.priority === "high" ? "high" : formData.priority === "low" ? "low" : "medium",
        successProbability: Math.floor(Math.random() * 30) + 70, // 70-100%
        aiSummary: `Caso de ${formData.type.toLowerCase()} con prioridad ${formData.priority === "high" ? "alta" : formData.priority === "medium" ? "media" : "baja"}. ${formData.description.slice(0, 100)}${formData.description.length > 100 ? "..." : ""}`,
        keyFacts: [
          `Tipo de caso: ${formData.type}`,
          `Cliente: ${formData.client}`,
          `Prioridad: ${formData.priority === "high" ? "Alta" : formData.priority === "medium" ? "Media" : "Baja"}`,
          formData.estimatedCost ? `Costo estimado: ${formData.estimatedCost}` : "Costo por definir",
        ],
        nextActions: [
          "Revisar documentación inicial",
          "Programar reunión con el cliente",
          "Definir estrategia legal",
          "Establecer cronograma de trabajo",
        ],
        milestones: formData.milestones
          .filter(m => m.title.trim())
          .map((milestone, index) => ({
            id: `m-${crypto.randomUUID().slice(0, 8)}-${index + 1}`,
            title: milestone.title,
            completed: false,
            dueDate: milestone.dueDate || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 días por defecto
            dependencies: index > 0 ? [`m-${crypto.randomUUID().slice(0, 8)}-${index}`] : [],
          })),
        messages: [
          {
            id: `msg-${crypto.randomUUID()}`,
            sender: "lawyer",
            content: `Hola ${formData.client}, he creado tu caso "${formData.title}". Estaré en contacto contigo para coordinar los próximos pasos.`,
            timestamp: new Date().toISOString(),
            status: "sent",
          },
        ],
        documents: [],
        activities: [
          {
            id: `act-${crypto.randomUUID()}`,
            type: "status_change",
            description: "Caso creado",
            timestamp: new Date().toISOString(),
            user: "lawyer",
          },
        ],
        unreadMessagesCount: 0,
      };

      // Simular delay de creación
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Guardar en sessionStorage (persistencia durante la sesión)
      const existingCases = JSON.parse(sessionStorage.getItem("cases") || "[]");
      const updatedCases = [newCase, ...existingCases];
      sessionStorage.setItem("cases", JSON.stringify(updatedCases));

      // Notificar al componente padre
      onCaseAdded(newCase);

      // Cerrar modal directamente después de crear el caso
      handleClose();
    } catch (error) {
      console.error("Error al crear el caso:", error);
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // Reset form
    setFormData({
      title: "",
      type: "",
      client: "",
      description: "",
      priority: "medium",
      estimatedCost: "",
      milestones: [{ title: "", dueDate: "" }],
    });
    setStep("basic");
    setErrors({});
    setIsSubmitting(false);
    onClose();
  };

  if (!isOpen) return null;

  const getStepTitle = () => {
    switch (step) {
      case "basic": return "Información Básica";
      case "details": return "Detalles del Caso";
      case "milestones": return "Tareas y Plazos";
      default: return "";
    }
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-6">
      {["basic", "details", "milestones"].map((stepName, index) => (
        <div key={stepName} className="flex items-center">
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              step === stepName
                ? "bg-blue-600 text-white"
                : index < ["basic", "details", "milestones"].indexOf(step)
                ? "bg-green-600 text-white"
                : "bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
            }`}
          >
            {index < ["basic", "details", "milestones"].indexOf(step) ? (
              <CheckCircleIcon className="h-5 w-5" />
            ) : (
              index + 1
            )}
          </div>
          {index < 2 && (
            <div
              className={`w-12 h-0.5 mx-2 ${
                index < ["basic", "details", "milestones"].indexOf(step)
                  ? "bg-green-600"
                  : "bg-gray-200 dark:bg-gray-700"
              }`}
            />
          )}
        </div>
      ))}
    </div>
  );

  return (
    <Dialog.Root open={isOpen} onOpenChange={handleClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 dark:bg-black/70 z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-50 w-full max-w-2xl p-6 max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between mb-6">
            <Dialog.Title className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              {getStepTitle()}
            </Dialog.Title>
            <Dialog.Close asChild>
              <button className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 cursor-pointer">
                <XMarkIcon className="h-6 w-6" />
              </button>
            </Dialog.Close>
          </div>

          {renderStepIndicator()}

          {/* Step Content */}
          <div className="space-y-6">
            {step === "basic" && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-4"
              >
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <UserIcon className="h-4 w-4 inline mr-1" />
                    Título del Caso *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    className={`w-full px-3 py-2 border rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.title ? "border-red-500" : "border-gray-300 dark:border-gray-600"
                    }`}
                    placeholder="Ej: Despido sin causa - Empresa XYZ"
                  />
                  {errors.title && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.title}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <ScaleIcon className="h-4 w-4 inline mr-1" />
                    Tipo de Caso *
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
                    className={`w-full px-3 py-2 border rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.type ? "border-red-500" : "border-gray-300 dark:border-gray-600"
                    }`}
                  >
                    <option value="">Seleccionar tipo de caso</option>
                    {CASE_TYPES.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                  {errors.type && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.type}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <UserIcon className="h-4 w-4 inline mr-1" />
                    Cliente *
                  </label>
                  <input
                    type="text"
                    value={formData.client}
                    onChange={(e) => setFormData(prev => ({ ...prev, client: e.target.value }))}
                    className={`w-full px-3 py-2 border rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.client ? "border-red-500" : "border-gray-300 dark:border-gray-600"
                    }`}
                    placeholder="Nombre completo del cliente"
                  />
                  {errors.client && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.client}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Prioridad
                  </label>
                  <select
                    value={formData.priority}
                    onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as "low" | "medium" | "high" }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {PRIORITIES.map(priority => (
                      <option key={priority.value} value={priority.value}>
                        {priority.label}
                      </option>
                    ))}
                  </select>
                </div>
              </motion.div>
            )}

            {step === "details" && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-4"
              >
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Descripción del Caso *
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={4}
                    className={`w-full px-3 py-2 border rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none ${
                      errors.description ? "border-red-500" : "border-gray-300 dark:border-gray-600"
                    }`}
                    placeholder="Describe los detalles del caso, antecedentes, situación actual..."
                  />
                  {errors.description && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.description}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Costo Estimado (Opcional)
                  </label>
                  <input
                    type="text"
                    value={formData.estimatedCost}
                    onChange={(e) => setFormData(prev => ({ ...prev, estimatedCost: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Ej: ARS 150,000 - 200,000"
                  />
                </div>
              </motion.div>
            )}

            {step === "milestones" && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="space-y-4"
              >
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Tareas y Plazos (Opcional)
                  </h4>
                  <button
                    type="button"
                    onClick={addMilestone}
                    className="flex items-center px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md transition-colors cursor-pointer"
                  >
                    <PlusIcon className="h-4 w-4 mr-1" />
                    Agregar Tarea
                  </button>
                </div>

                <div className="space-y-3 max-h-60 overflow-y-auto">
                  {formData.milestones.map((milestone, index) => (
                    <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                      <div className="flex-1">
                        <input
                          type="text"
                          value={milestone.title}
                          onChange={(e) => updateMilestone(index, "title", e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500"
                          placeholder="Nombre de la tarea"
                        />
                      </div>
                      <div className="w-32">
                        <input
                          type="date"
                          value={milestone.dueDate}
                          onChange={(e) => updateMilestone(index, "dueDate", e.target.value)}
                          className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </div>
                      {formData.milestones.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeMilestone(index)}
                          className="text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 p-1 rounded cursor-pointer"
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  ))}
                </div>

                {formData.milestones.length === 0 && (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <CalendarIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No hay tareas agregadas</p>
                    <button
                      type="button"
                      onClick={addMilestone}
                      className="mt-2 text-blue-600 dark:text-blue-400 hover:underline cursor-pointer"
                    >
                      Agregar primera tarea
                    </button>
                  </div>
                )}
              </motion.div>
            )}


          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            {step !== "basic" && (
              <button
                type="button"
                onClick={handleBack}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer"
              >
                Anterior
              </button>
            )}

            <div className="flex-1" />

            <button
              type="button"
              onClick={handleNext}
              disabled={isSubmitting}
              className="px-6 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors cursor-pointer flex items-center"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creando...
                </>
              ) : step === "milestones" ? (
                "Crear Caso"
              ) : (
                "Siguiente"
              )}
            </button>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
