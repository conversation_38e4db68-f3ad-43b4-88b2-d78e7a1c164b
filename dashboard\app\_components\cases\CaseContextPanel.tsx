"use client";

import { motion } from "framer-motion";
import {
  CheckCircleIcon,
  LightBulbIcon,
  ChatBubbleLeftRightIcon,
  DocumentIcon,
  ScaleIcon,
  ChartBarIcon,
  CpuChipIcon,
} from "@heroicons/react/24/outline";
import { Case } from "../../_lib/types";
import config from "../../../public/data/config.json";

interface CaseContextPanelProps {
  case: Case;
  onTabChange: (tab: string) => void;
}

// Removed unused riskColors and complexityColors constants

export function CaseContextPanel({
  case: caseData,
  onTabChange,
}: CaseContextPanelProps) {
  // Funciones para calcular mini resúmenes
  const getUnreadMessagesCount = () => {
    // Simular mensajes no leídos (en una app real vendría del estado)
    const unreadCount =
      caseData.messages?.filter((msg) => msg.status !== "read").length || 0;
    return unreadCount;
  };

  const getOverdueTasks = () => {
    const now = new Date();
    const overdue =
      caseData.milestones?.filter(
        (milestone) => !milestone.completed && new Date(milestone.dueDate) < now
      ).length || 0;
    return overdue;
  };

  const getRecentDocuments = () => {
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
    const recent =
      caseData.documents?.filter(
        (doc) => new Date(doc.uploadedAt) > threeDaysAgo
      ).length || 0;
    return recent;
  };

  const getRecentActivities = () => {
    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);
    const recent =
      caseData.activities?.filter(
        (activity) => new Date(activity.timestamp) > oneDayAgo
      ).length || 0;
    return recent;
  };

  const getTasksProgress = () => {
    const total = caseData.milestones?.length || 0;
    const completed =
      caseData.milestones?.filter((m) => m.completed).length || 0;
    return { completed, total };
  };

  // Configuración de elementos de navegación con mini resúmenes
  const unreadMessages = getUnreadMessagesCount();
  const overdueTasks = getOverdueTasks();
  const recentDocs = getRecentDocuments();
  const recentActivities = getRecentActivities();
  const tasksProgress = getTasksProgress();

  const allNavigationItems = [
    {
      id: "chat",
      label: "Mensajes",
      icon: ChatBubbleLeftRightIcon,
      iconColor:
        "text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300",
      value: caseData.messages?.length || 0,
      unit: "conversaciones",
      summary:
        unreadMessages > 0 ? `${unreadMessages} sin leer` : "Todo al día",
      summaryColor:
        unreadMessages > 0
          ? "text-red-600 dark:text-red-400"
          : "text-green-600 dark:text-green-400",
    },
    {
      id: "tasks",
      label: "Tareas",
      icon: CheckCircleIcon,
      iconColor:
        "text-green-600 dark:text-green-400 group-hover:text-green-700 dark:group-hover:text-green-300",
      value: caseData.milestones?.length || 0,
      unit: "hitos",
      summary:
        overdueTasks > 0
          ? `${overdueTasks} vencidas`
          : `${tasksProgress.completed}/${tasksProgress.total} completadas`,
      summaryColor:
        overdueTasks > 0
          ? "text-red-600 dark:text-red-400"
          : "text-green-600 dark:text-green-400",
    },
    {
      id: "documents",
      label: "Documentos",
      icon: DocumentIcon,
      iconColor:
        "text-orange-600 dark:text-orange-400 group-hover:text-orange-700 dark:group-hover:text-orange-300",
      value: caseData.documents?.length || 0,
      unit: "archivos",
      summary: recentDocs > 0 ? `${recentDocs} nuevos` : "Sin cambios",
      summaryColor:
        recentDocs > 0
          ? "text-blue-600 dark:text-blue-400"
          : "text-gray-500 dark:text-gray-400",
    },
    {
      id: "jurisprudence",
      label: "Jurisprudencia",
      icon: ScaleIcon,
      iconColor:
        "text-indigo-600 dark:text-indigo-400 group-hover:text-indigo-700 dark:group-hover:text-indigo-300",
      value: "Ver",
      unit: "referencias",
      summary: "Buscar casos similares",
      summaryColor: "text-indigo-600 dark:text-indigo-400",
    },
    {
      id: "history",
      label: "Historial",
      icon: ChartBarIcon,
      iconColor:
        "text-gray-600 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300",
      value: caseData.activities?.length || 0,
      unit: "actividades",
      summary:
        recentActivities > 0
          ? `${recentActivities} recientes`
          : "Sin actividad reciente",
      summaryColor:
        recentActivities > 0
          ? "text-blue-600 dark:text-blue-400"
          : "text-gray-500 dark:text-gray-400",
    },
    {
      id: "intelligence",
      label: "Inteligencia",
      icon: CpuChipIcon,
      iconColor:
        "text-cyan-600 dark:text-cyan-400 group-hover:text-cyan-700 dark:group-hover:text-cyan-300",
      value: "IA",
      unit: "análisis",
      summary: `${Math.round(caseData.progress * 100)}% progreso`,
      summaryColor: "text-cyan-600 dark:text-cyan-400",
    },
  ];

  // Filtrar elementos de navegación basado en feature flags - SOLO RENDERIZADO
  const navigationItems = allNavigationItems.filter((item) => {
    if (item.id === "intelligence") {
      return config.featureFlags.caseDetails.intelligenceTab.enabled;
    }
    return true;
  });

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 space-y-6">
      {/* AI Summary */}
      {((caseData.keyFacts?.length ?? 0) > 0 ||
        (caseData.nextActions?.length ?? 0) > 0) && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-blue-50 dark:bg-blue-900 rounded-lg p-4"
        >
          <div className="flex items-start space-x-3">
            <LightBulbIcon className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
            <div className="w-full">
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-4">
                Resumen IA del Caso
              </h4>

              {/* Grid layout for Key Facts and Next Actions */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Key Facts */}
                {caseData.keyFacts && caseData.keyFacts.length > 0 && (
                  <div>
                    <h5 className="text-xs font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center">
                      <CheckCircleIcon className="h-3 w-3 mr-1" />
                      Hechos Clave
                    </h5>
                    <ul className="space-y-1">
                      {caseData.keyFacts.map((fact, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <div className="w-1 h-1 bg-blue-600 dark:bg-blue-400 rounded-full mt-2 flex-shrink-0" />
                          <span className="text-xs text-blue-800 dark:text-blue-200 leading-relaxed">
                            {fact}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Next Actions */}
                {caseData.nextActions && caseData.nextActions.length > 0 && (
                  <div>
                    <h5 className="text-xs font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center">
                      <LightBulbIcon className="h-3 w-3 mr-1" />
                      Recomendaciones IA
                    </h5>
                    <ul className="space-y-1">
                      {caseData.nextActions.map((action, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <div className="w-1 h-1 bg-blue-600 dark:bg-blue-400 rounded-full mt-2 flex-shrink-0" />
                          <span className="text-xs text-blue-800 dark:text-blue-200 leading-relaxed">
                            {action}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Quick Navigation */}
      <div className="grid grid-cols-2 lg:grid-cols-3 gap-4">
        {navigationItems.map((item) => {
          const IconComponent = item.icon;
          return (
            <button
              key={item.id}
              onClick={() => onTabChange(item.id)}
              className="bg-gray-50 cursor-pointer dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg p-4 text-left transition-colors group"
            >
              <div className="flex items-center space-x-2 mb-2">
                <IconComponent className={`h-4 w-4 ${item.iconColor}`} />
                <span className="text-xs font-medium text-gray-600 dark:text-gray-300 group-hover:text-gray-700 dark:group-hover:text-gray-200">
                  {item.label}
                </span>
              </div>
              <div className="flex items-baseline space-x-1 mb-1">
                <span className="text-lg font-bold text-gray-900 dark:text-gray-100">
                  {item.value}
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {item.unit}
                </span>
              </div>
              {/* Mini resumen */}
              <div className="text-xs font-medium">
                <span className={item.summaryColor}>{item.summary}</span>
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );
}
