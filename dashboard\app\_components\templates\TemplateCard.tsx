"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import {
  StarIcon,
  ArrowDownTrayIcon,
  CurrencyDollarIcon,
  EyeIcon,
} from "@heroicons/react/24/outline";
import { StarIcon as StarSolidIcon } from "@heroicons/react/24/solid";
import { Template, UserTemplate } from "../../_lib/types";
import { PurchaseModal } from "../modals/PurchaseModal";

interface TemplateCardProps {
  template: Template;
  featured?: boolean;
  onPurchase?: (template: Template) => void;
}

const legalAreaColors = {
  Laboral: "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200",
  Civil:
    "bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200",
  Familia: "bg-pink-100 dark:bg-pink-900 text-pink-800 dark:text-pink-200",
  Penal: "bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200",
  Comercial:
    "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200",
};

export function TemplateCard({
  template,
  featured = false,
  onPurchase,
}: TemplateCardProps) {
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownload = async () => {
    // Call onPurchase callback if provided
    if (onPurchase) {
      onPurchase(template);
    }

    if (template.price > 0) {
      setShowPurchaseModal(true);
      return;
    }

    // Free template - direct download
    setIsDownloading(true);

    // Simulate download delay
    setTimeout(() => {
      // In a real app, this would trigger the actual download
      console.log("Downloading template:", template.title);

      // Save to user's library
      const userTemplates = JSON.parse(
        localStorage.getItem("userTemplates") || "[]"
      );
      const newUserTemplate = {
        id: `user-${crypto.randomUUID()}`,
        templateId: template.id,
        purchasedAt: new Date().toISOString(),
        downloadCount: 1,
      };

      if (
        !userTemplates.find((ut: UserTemplate) => ut.templateId === template.id)
      ) {
        userTemplates.push(newUserTemplate);
        localStorage.setItem("userTemplates", JSON.stringify(userTemplates));
      }

      setIsDownloading(false);
    }, 1500);
  };

  const handlePurchaseComplete = () => {
    setShowPurchaseModal(false);
    // Add to user's library after purchase
    const userTemplates = JSON.parse(
      localStorage.getItem("userTemplates") || "[]"
    );
    const newUserTemplate = {
      id: `user-${crypto.randomUUID()}`,
      templateId: template.id,
      purchasedAt: new Date().toISOString(),
      downloadCount: 1,
    };

    if (
      !userTemplates.find((ut: UserTemplate) => ut.templateId === template.id)
    ) {
      userTemplates.push(newUserTemplate);
      localStorage.setItem("userTemplates", JSON.stringify(userTemplates));
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <StarSolidIcon key={i} className="h-4 w-4 text-yellow-400" role="img" />
      );
    }

    if (hasHalfStar) {
      stars.push(
        <div key="half" className="relative" role="img">
          <StarIcon className="h-4 w-4 text-yellow-400" />
          <StarSolidIcon
            className="h-4 w-4 text-yellow-400 absolute top-0 left-0"
            style={{ clipPath: "inset(0 50% 0 0)" }}
          />
        </div>
      );
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <StarIcon
          key={`empty-${i}`}
          className="h-4 w-4 text-gray-300 dark:text-gray-600"
          role="img"
        />
      );
    }

    return stars;
  };

  return (
    <>
      <motion.div
        whileHover={{ y: -4 }}
        transition={{ duration: 0.2 }}
        className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow overflow-hidden ${
          featured ? "ring-2 ring-yellow-400" : ""
        }`}
      >
        {/* Template Preview */}
        <div className="relative h-48 bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
          <div className="text-6xl text-gray-300 dark:text-gray-500">📄</div>
          {featured && (
            <div className="absolute top-2 left-2">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                <StarSolidIcon className="h-3 w-3 mr-1" />
                Destacado
              </span>
            </div>
          )}
          <div className="absolute top-2 right-2">
            <button
              className="p-1 bg-white dark:bg-gray-800 rounded-full shadow-sm hover:shadow-md transition-shadow cursor-pointer"
              aria-label="Vista previa"
            >
              <EyeIcon className="h-4 w-4 text-gray-600 dark:text-gray-400" />
            </button>
          </div>
        </div>

        <div className="p-4 space-y-3">
          {/* Header */}
          <div className="space-y-2">
            <div className="flex items-start justify-between">
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 text-sm leading-tight line-clamp-2">
                {template.title}
              </h3>
              <div className="flex items-center space-x-1 ml-2">
                {template.price === 0 ? (
                  <span className="text-green-600 dark:text-green-400 font-semibold text-sm">
                    Gratis
                  </span>
                ) : (
                  <div className="flex items-center text-blue-600 dark:text-blue-400">
                    <CurrencyDollarIcon className="h-3 w-3" />
                    <span className="font-semibold text-sm">
                      {template.price.toLocaleString("es-AR")}
                    </span>
                  </div>
                )}
              </div>
            </div>

            <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2">
              {template.description}
            </p>
          </div>

          {/* Tags */}
          <div className="flex items-center space-x-2">
            <span
              className={`text-xs font-medium px-2 py-1 rounded-full ${
                legalAreaColors[
                  template.legalArea as keyof typeof legalAreaColors
                ] ||
                "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"
              }`}
            >
              {template.legalArea}
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
              {template.documentType}
            </span>
          </div>

          {/* Rating and Downloads */}
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center space-x-1">
              <div className="flex items-center">
                {renderStars(template.rating)}
              </div>
              <span className="text-gray-600 dark:text-gray-400">
                ({template.rating})
              </span>
            </div>
            <div className="flex items-center space-x-1 text-gray-500 dark:text-gray-400">
              <ArrowDownTrayIcon className="h-3 w-3" />
              <span>{template.downloadCount.toLocaleString("en-US")}</span>
            </div>
          </div>

          {/* Author */}
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Por {template.author}
          </div>

          {/* Action Button */}
          <button
            onClick={handleDownload}
            disabled={isDownloading}
            className={`w-full py-2 px-3 text-sm font-medium rounded-md transition-colors cursor-pointer ${
              template.price === 0
                ? "bg-green-600 text-white hover:bg-green-700 focus:ring-green-500"
                : "bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500"
            } focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed`}
          >
            {isDownloading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Descargando...
              </div>
            ) : template.price === 0 ? (
              <div className="flex items-center justify-center">
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                Descargar Gratis
              </div>
            ) : (
              <div className="flex items-center justify-center">
                <CurrencyDollarIcon className="h-4 w-4 mr-2" />
                Comprar ARS {template.price.toLocaleString("es-AR")}
              </div>
            )}
          </button>
        </div>
      </motion.div>

      {/* Purchase Modal */}
      {showPurchaseModal && (
        <PurchaseModal
          template={template}
          onClose={() => setShowPurchaseModal(false)}
          onPurchaseComplete={handlePurchaseComplete}
        />
      )}
    </>
  );
}
