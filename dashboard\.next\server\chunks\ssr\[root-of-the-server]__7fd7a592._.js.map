{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_9e72d27f-module__JKMi0a__className\",\n  \"variable\": \"inter_9e72d27f-module__JKMi0a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/layout/ClientLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ClientLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call ClientLayout() from the server but ClientLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/_components/layout/ClientLayout.tsx <module evaluation>\",\n    \"ClientLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yEACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/layout/ClientLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ClientLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call ClientLayout() from the server but ClientLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/_components/layout/ClientLayout.tsx\",\n    \"ClientLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qDACA", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_contexts/ThemeContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/_contexts/ThemeContext.tsx <module evaluation>\",\n    \"ThemeProvider\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/_contexts/ThemeContext.tsx <module evaluation>\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,gEACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,gEACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_contexts/ThemeContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/_contexts/ThemeContext.tsx\",\n    \"ThemeProvider\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/_contexts/ThemeContext.tsx\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,4CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,4CACA", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\r\nimport { Inter } from \"next/font/google\";\r\nimport \"./globals.css\";\r\nimport { ClientLayout } from \"./_components/layout/ClientLayout\";\r\nimport { ThemeProvider } from \"./_contexts/ThemeContext\";\r\n\r\nconst inter = Inter({\r\n  subsets: [\"latin\"],\r\n  variable: \"--font-inter\",\r\n});\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"X-Legal Dashboard\",\r\n  description: \"Plataforma legal inteligente para abogados\",\r\n};\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <html lang=\"es-AR\" suppressHydrationWarning>\r\n      <head>\r\n        <meta\r\n          name=\"format-detection\"\r\n          content=\"telephone=no, date=no, email=no, address=no\"\r\n        />\r\n        <script\r\n          dangerouslySetInnerHTML={{\r\n            __html: `\r\n              (function() {\r\n                try {\r\n                  const theme = localStorage.getItem('theme') || 'system';\r\n                  const isDark = theme === 'dark' || \r\n                    (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches);\r\n                  \r\n                  if (isDark) {\r\n                    document.documentElement.classList.add('dark');\r\n                  }\r\n                } catch (e) {\r\n                  // Silent fail\r\n                }\r\n              })();\r\n            `,\r\n          }}\r\n        />\r\n      </head>\r\n      <body\r\n        className={`${inter.variable} font-sans antialiased bg-gray-50 dark:bg-gray-900 transition-colors`}\r\n      >\r\n        <ThemeProvider>\r\n          <ClientLayout>\r\n            {children}\r\n          </ClientLayout>\r\n        </ThemeProvider>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;;;;;;AAOO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAQ,wBAAwB;;0BACzC,8OAAC;;kCACC,8OAAC;wBACC,MAAK;wBACL,SAAQ;;;;;;kCAEV,8OAAC;wBACC,yBAAyB;4BACvB,QAAQ,CAAC;;;;;;;;;;;;;;YAcT,CAAC;wBACH;;;;;;;;;;;;0BAGJ,8OAAC;gBACC,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,oEAAoE,CAAC;0BAElG,cAAA,8OAAC,iIAAA,CAAA,gBAAa;8BACZ,cAAA,8OAAC,6IAAA,CAAA,eAAY;kCACV;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}