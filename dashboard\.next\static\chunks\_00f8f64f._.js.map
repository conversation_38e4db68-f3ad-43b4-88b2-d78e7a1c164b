{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/ui/Logo.tsx"], "sourcesContent": ["interface LogoProps {\r\n  size?: number;\r\n  className?: string;\r\n}\r\n\r\nexport function Logo({ size = 32, className = \"\" }: LogoProps) {\r\n  return (\r\n    <svg\r\n      width={size}\r\n      height={size}\r\n      viewBox=\"0 0 32 32\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      className={`stroke-current ${className}`}\r\n      role=\"img\"\r\n      aria-label=\"X-Legal Logo\"\r\n    >\r\n      {/* Background rectangle with rounded corners for clean geometric design */}\r\n      <rect\r\n        x=\"1\"\r\n        y=\"1\"\r\n        width=\"30\"\r\n        height=\"30\"\r\n        rx=\"15\"\r\n        className=\"fill-sky-100 dark:fill-slate-800 stroke-sky-200 dark:stroke-slate-700\"\r\n        strokeWidth=\"1\"\r\n      />\r\n\r\n      {/* Scales of justice using clean geometric shapes */}\r\n      <g strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\">\r\n        {/* Central pillar - rectangle for clean geometry */}\r\n        <rect\r\n          x=\"15\"\r\n          y=\"7\"\r\n          width=\"2\"\r\n          height=\"13\"\r\n          className=\"fill-sky-600 dark:fill-sky-400\"\r\n        />\r\n\r\n        {/* Base - rectangle */}\r\n        <rect\r\n          x=\"11\"\r\n          y=\"23\"\r\n          width=\"10\"\r\n          height=\"2\"\r\n          className=\"fill-sky-600 dark:fill-sky-400\"\r\n        />\r\n\r\n        {/* Horizontal beam - rectangle */}\r\n        <rect\r\n          x=\"10\"\r\n          y=\"11\"\r\n          width=\"12\"\r\n          height=\"2\"\r\n          className=\"fill-sky-600 dark:fill-sky-400\"\r\n        />\r\n\r\n        {/* Left scale pan - ellipse for clean geometric design */}\r\n        <ellipse\r\n          cx=\"10\"\r\n          cy=\"16\"\r\n          rx=\"3\"\r\n          ry=\"1.5\"\r\n          className=\"fill-transparent stroke-sky-600 dark:stroke-sky-400\"\r\n        />\r\n\r\n        {/* Right scale pan - ellipse for clean geometric design */}\r\n        <ellipse\r\n          cx=\"22\"\r\n          cy=\"16\"\r\n          rx=\"3\"\r\n          ry=\"1.5\"\r\n          className=\"fill-transparent stroke-sky-600 dark:stroke-sky-400\"\r\n        />\r\n\r\n        {/* Left chain - simple rectangle */}\r\n        <rect\r\n          x=\"9.5\"\r\n          y=\"12\"\r\n          width=\"1\"\r\n          height=\"4\"\r\n          className=\"fill-sky-600 dark:fill-sky-400\"\r\n        />\r\n\r\n        {/* Right chain - simple rectangle */}\r\n        <rect\r\n          x=\"21.5\"\r\n          y=\"12\"\r\n          width=\"1\"\r\n          height=\"4\"\r\n          className=\"fill-sky-600 dark:fill-sky-400\"\r\n        />\r\n      </g>\r\n    </svg>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAKO,SAAS,KAAK,EAAE,OAAO,EAAE,EAAE,YAAY,EAAE,EAAa;IAC3D,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW,CAAC,eAAe,EAAE,WAAW;QACxC,MAAK;QACL,cAAW;;0BAGX,6LAAC;gBACC,GAAE;gBACF,GAAE;gBACF,OAAM;gBACN,QAAO;gBACP,IAAG;gBACH,WAAU;gBACV,aAAY;;;;;;0BAId,6LAAC;gBAAE,eAAc;gBAAQ,gBAAe;gBAAQ,aAAY;;kCAE1D,6LAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,WAAU;;;;;;kCAIZ,6LAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,WAAU;;;;;;kCAIZ,6LAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,WAAU;;;;;;kCAIZ,6LAAC;wBACC,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,WAAU;;;;;;kCAIZ,6LAAC;wBACC,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,IAAG;wBACH,WAAU;;;;;;kCAIZ,6LAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,WAAU;;;;;;kCAIZ,6LAAC;wBACC,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,WAAU;;;;;;;;;;;;;;;;;;AAKpB;KA1FgB", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/layout/Sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport {\r\n  FolderIcon,\r\n  DocumentTextIcon,\r\n  ChartBarIcon,\r\n  Cog6ToothIcon,\r\n  ChevronLeftIcon,\r\n  ChevronRightIcon,\r\n  XMarkIcon,\r\n} from \"@heroicons/react/24/outline\";\r\nimport { Logo } from \"../ui/Logo\";\r\nimport config from \"../../../public/data/config.json\";\r\n\r\nconst allNavigation = [\r\n  { name: \"Casos\", href: \"/dashboard\", icon: FolderIcon },\r\n  { name: \"Plantillas\", href: \"/dashboard/templates\", icon: DocumentTextIcon },\r\n  { name: \"Analytics\", href: \"/dashboard/analytics\", icon: ChartBarIcon },\r\n  { name: \"Ajustes\", href: \"/dashboard/settings\", icon: Cog6ToothIcon },\r\n];\r\n\r\n// Filtrar navegación basado en feature flags - SOLO RENDERIZADO\r\nconst navigation = allNavigation.filter((item) => {\r\n  if (item.name === \"Analytics\") {\r\n    return config.featureFlags.navigation.analyticsMenu.enabled;\r\n  }\r\n  return true;\r\n});\r\n\r\ninterface SidebarProps {\r\n  isMobileOpen?: boolean;\r\n  onMobileToggle?: () => void;\r\n}\r\n\r\nexport function Sidebar({\r\n  isMobileOpen = false,\r\n  onMobileToggle,\r\n}: SidebarProps = {}) {\r\n  const [isCollapsed, setIsCollapsed] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n\r\n  const handleLogoClick = () => {\r\n    router.push(\"/dashboard\");\r\n    // Cerrar sidebar móvil si está abierto\r\n    if (onMobileToggle && isMobileOpen) {\r\n      onMobileToggle();\r\n    }\r\n  };\r\n\r\n  // Load sidebar state from localStorage on mount (solo para desktop)\r\n  useEffect(() => {\r\n    const saved = localStorage.getItem(\"sidebar-collapsed\");\r\n    if (saved) {\r\n      setIsCollapsed(JSON.parse(saved));\r\n    }\r\n  }, []);\r\n\r\n  // Save sidebar state to localStorage (solo para desktop)\r\n  const toggleSidebar = () => {\r\n    const newState = !isCollapsed;\r\n    setIsCollapsed(newState);\r\n    localStorage.setItem(\"sidebar-collapsed\", JSON.stringify(newState));\r\n  };\r\n\r\n  // Cerrar sidebar móvil al hacer clic en un enlace (solo en móvil/tablet)\r\n  const handleLinkClick = () => {\r\n    // Solo cerrar en móvil/tablet cuando el sidebar está abierto como overlay\r\n    if (onMobileToggle && isMobileOpen) {\r\n      onMobileToggle();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Overlay para móvil/tablet */}\r\n      {isMobileOpen && (\r\n        <div\r\n          className=\"fixed inset-0 bg-black opacity-50 z-[35] lg:hidden backdrop-blur-sm\"\r\n          onClick={onMobileToggle}\r\n        />\r\n      )}\r\n\r\n      {/* Botón discreto para expandir sidebar - sale del borde */}\r\n      <button\r\n        onClick={toggleSidebar}\r\n        className={`hidden lg:flex fixed top-20 cursor-pointer left-[79px] z-90 w-6 h-8 bg-white dark:bg-gray-800 border border-l-0 border-gray-200 dark:border-gray-700 rounded-r-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 items-center justify-center ${\r\n          isCollapsed\r\n            ? \"opacity-100 translate-x-0 transition-all duration-500 delay-300\"\r\n            : \"opacity-0 translate-x-[-100%] pointer-events-none transition-all duration-200\"\r\n        }`}\r\n        aria-label=\"Expandir sidebar\"\r\n      >\r\n        <ChevronRightIcon className=\"h-3 w-3 text-gray-500 dark:text-gray-400\" />\r\n      </button>\r\n\r\n      {/* Sidebar */}\r\n      <div\r\n        className={`\r\n          bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 transition-all duration-300\r\n\r\n          ${\r\n            isMobileOpen\r\n              ? \"fixed top-0 bottom-0 left-0 z-[45] w-64 flex flex-col lg:hidden\"\r\n              : \"hidden lg:flex\"\r\n          }\r\n\r\n          lg:flex-col lg:relative lg:inset-auto lg:z-auto\r\n          ${isCollapsed ? \"lg:w-20\" : \"lg:w-64\"}\r\n        `}\r\n      >\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Header */}\r\n          <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\r\n            {/* Logo y título */}\r\n            <div\r\n              className={`flex items-center cursor-pointer hover:opacity-80 transition-opacity ${\r\n                isCollapsed ? \"lg:justify-center lg:w-full\" : \"space-x-3\"\r\n              }`}\r\n              onClick={handleLogoClick}\r\n            >\r\n              <Logo size={32} className=\"flex-shrink-0\" />\r\n              {/* En desktop: mostrar título solo si no está colapsado */}\r\n              {/* En móvil: siempre mostrar título */}\r\n              <h1\r\n                className={`text-xl font-bold text-gray-900 dark:text-gray-100 ${\r\n                  isCollapsed ? \"lg:hidden\" : \"block\"\r\n                }`}\r\n              >\r\n                X-Legal\r\n              </h1>\r\n            </div>\r\n\r\n            {/* Botones de control - solo mostrar cuando no está colapsado en desktop */}\r\n            <div\r\n              className={`flex items-center space-x-2 ${\r\n                isCollapsed ? \"lg:hidden\" : \"\"\r\n              }`}\r\n            >\r\n              {/* Botón cerrar para móvil */}\r\n              <button\r\n                onClick={onMobileToggle}\r\n                className=\"lg:hidden p-1.5 cursor-pointer rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\"\r\n                aria-label=\"Cerrar menú\"\r\n              >\r\n                <XMarkIcon className=\"h-5 w-5 text-gray-500 dark:text-gray-400\" />\r\n              </button>\r\n\r\n              {/* Botón colapsar para desktop */}\r\n              <button\r\n                onClick={toggleSidebar}\r\n                className=\"hidden lg:block p-1.5 cursor-pointer rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\"\r\n                aria-label=\"Colapsar sidebar\"\r\n              >\r\n                <ChevronLeftIcon className=\"h-5 w-5 text-gray-500 dark:text-gray-400\" />\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"flex-1 px-4 py-6 space-y-2\">\r\n            {navigation.map((item) => {\r\n              const isActive = pathname === item.href;\r\n              return (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={handleLinkClick}\r\n                  className={`flex items-center ${\r\n                    isCollapsed ? \"justify-center px-2 py-3\" : \"px-3 py-2\"\r\n                  } rounded-md text-sm font-medium transition-colors ${\r\n                    isActive\r\n                      ? \"bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400 border-r-2 border-blue-700 dark:border-blue-400\"\r\n                      : \"text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  }`}\r\n                  title={isCollapsed ? item.name : undefined}\r\n                >\r\n                  <item.icon\r\n                    className={`${\r\n                      isCollapsed ? \"h-6 w-6 mx-auto\" : \"h-5 w-5 mr-3\"\r\n                    } ${\r\n                      isActive\r\n                        ? \"text-blue-700 dark:text-blue-400\"\r\n                        : \"text-gray-400 dark:text-gray-500\"\r\n                    }`}\r\n                  />\r\n                  {!isCollapsed && <span>{item.name}</span>}\r\n                </Link>\r\n              );\r\n            })}\r\n          </nav>\r\n\r\n          {/* Footer */}\r\n          <div className=\"p-4 border-t border-gray-200 dark:border-gray-700\">\r\n            {!isCollapsed && (\r\n              <div className=\"text-xs text-gray-500 dark:text-gray-400 text-center\">\r\n                © 2025 X-Legal SRL\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;AAfA;;;;;;;AAiBA,MAAM,gBAAgB;IACpB;QAAE,MAAM;QAAS,MAAM;QAAc,MAAM,sNAAA,CAAA,aAAU;IAAC;IACtD;QAAE,MAAM;QAAc,MAAM;QAAwB,MAAM,kOAAA,CAAA,mBAAgB;IAAC;IAC3E;QAAE,MAAM;QAAa,MAAM;QAAwB,MAAM,0NAAA,CAAA,eAAY;IAAC;IACtE;QAAE,MAAM;QAAW,MAAM;QAAuB,MAAM,4NAAA,CAAA,gBAAa;IAAC;CACrE;AAED,gEAAgE;AAChE,MAAM,aAAa,cAAc,MAAM,CAAC,CAAC;IACvC,IAAI,KAAK,IAAI,KAAK,aAAa;QAC7B,OAAO,gGAAA,CAAA,UAAM,CAAC,YAAY,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO;IAC7D;IACA,OAAO;AACT;AAOO,SAAS,QAAQ,EACtB,eAAe,KAAK,EACpB,cAAc,EACD,GAAG,CAAC,CAAC;;IAClB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,kBAAkB;QACtB,OAAO,IAAI,CAAC;QACZ,uCAAuC;QACvC,IAAI,kBAAkB,cAAc;YAClC;QACF;IACF;IAEA,oEAAoE;IACpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,OAAO;gBACT,eAAe,KAAK,KAAK,CAAC;YAC5B;QACF;4BAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,gBAAgB;QACpB,MAAM,WAAW,CAAC;QAClB,eAAe;QACf,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;IAC3D;IAEA,yEAAyE;IACzE,MAAM,kBAAkB;QACtB,0EAA0E;QAC1E,IAAI,kBAAkB,cAAc;YAClC;QACF;IACF;IAEA,qBACE;;YAEG,8BACC,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,6LAAC;gBACC,SAAS;gBACT,WAAW,CAAC,gPAAgP,EAC1P,cACI,oEACA,iFACJ;gBACF,cAAW;0BAEX,cAAA,6LAAC,kOAAA,CAAA,mBAAgB;oBAAC,WAAU;;;;;;;;;;;0BAI9B,6LAAC;gBACC,WAAW,CAAC;;;UAGV,EACE,eACI,oEACA,iBACL;;;UAGD,EAAE,cAAc,YAAY,UAAU;QACxC,CAAC;0BAED,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCACC,WAAW,CAAC,qEAAqE,EAC/E,cAAc,gCAAgC,aAC9C;oCACF,SAAS;;sDAET,6LAAC,oIAAA,CAAA,OAAI;4CAAC,MAAM;4CAAI,WAAU;;;;;;sDAG1B,6LAAC;4CACC,WAAW,CAAC,mDAAmD,EAC7D,cAAc,cAAc,SAC5B;sDACH;;;;;;;;;;;;8CAMH,6LAAC;oCACC,WAAW,CAAC,4BAA4B,EACtC,cAAc,cAAc,IAC5B;;sDAGF,6LAAC;4CACC,SAAS;4CACT,WAAU;4CACV,cAAW;sDAEX,cAAA,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAIvB,6LAAC;4CACC,SAAS;4CACT,WAAU;4CACV,cAAW;sDAEX,cAAA,6LAAC,gOAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAMjC,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS;oCACT,WAAW,CAAC,kBAAkB,EAC5B,cAAc,6BAA6B,YAC5C,kDAAkD,EACjD,WACI,oHACA,yHACJ;oCACF,OAAO,cAAc,KAAK,IAAI,GAAG;;sDAEjC,6LAAC,KAAK,IAAI;4CACR,WAAW,GACT,cAAc,oBAAoB,eACnC,CAAC,EACA,WACI,qCACA,oCACJ;;;;;;wCAEH,CAAC,6BAAe,6LAAC;sDAAM,KAAK,IAAI;;;;;;;mCArB5B,KAAK,IAAI;;;;;4BAwBpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;sCACZ,CAAC,6BACA,6LAAC;gCAAI,WAAU;0CAAuD;;;;;;;;;;;;;;;;;;;;;;;;AASpF;GA1KgB;;QAKG,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;;;KANV", "debugId": null}}, {"offset": {"line": 433, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_contexts/ThemeContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { createContext, useContext, useEffect, useState } from \"react\";\r\n\r\ntype Theme = \"light\" | \"dark\" | \"system\";\r\n\r\ninterface ThemeContextType {\r\n  theme: Theme;\r\n  setTheme: (theme: Theme) => void;\r\n  resolvedTheme: \"light\" | \"dark\";\r\n}\r\n\r\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\r\n\r\n// Helper function to detect system theme preference\r\nfunction getSystemTheme(): \"light\" | \"dark\" {\r\n  if (typeof window === \"undefined\") return \"dark\";\r\n  if (!window.matchMedia) return \"dark\"; // Fallback to dark for test environment compatibility\r\n  return window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\r\n}\r\n\r\n// Helper function to apply theme to DOM\r\nfunction applyTheme(theme: Theme): \"light\" | \"dark\" {\r\n  const root = document.documentElement;\r\n  const isDark = theme === \"dark\" || (theme === \"system\" && getSystemTheme() === \"dark\");\r\n  \r\n  if (isDark) {\r\n    root.classList.add(\"dark\");\r\n  } else {\r\n    root.classList.remove(\"dark\");\r\n  }\r\n  \r\n  return isDark ? \"dark\" : \"light\";\r\n}\r\n\r\nexport function ThemeProvider({ children }: { children: React.ReactNode }) {\r\n  const [theme, setTheme] = useState<Theme>(\"system\");\r\n  const [resolvedTheme, setResolvedTheme] = useState<\"light\" | \"dark\">(\"light\");\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  // Initialize theme on mount\r\n  useEffect(() => {\r\n    const savedTheme = (localStorage.getItem(\"theme\") as Theme) || \"system\";\r\n    setTheme(savedTheme);\r\n    \r\n    // Always save to localStorage to ensure persistence\r\n    localStorage.setItem(\"theme\", savedTheme);\r\n    \r\n    const resolved = applyTheme(savedTheme);\r\n    setResolvedTheme(resolved);\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  // Handle theme changes\r\n  const handleSetTheme = (newTheme: Theme) => {\r\n    setTheme(newTheme);\r\n    localStorage.setItem(\"theme\", newTheme);\r\n    \r\n    const resolved = applyTheme(newTheme);\r\n    setResolvedTheme(resolved);\r\n  };\r\n\r\n  // Listen for system theme changes\r\n  useEffect(() => {\r\n    if (!mounted || theme !== \"system\") return;\r\n\r\n    const mediaQuery = window.matchMedia?.(\"(prefers-color-scheme: dark)\");\r\n    if (!mediaQuery) return;\r\n\r\n    const handleChange = () => {\r\n      const resolved = applyTheme(\"system\");\r\n      setResolvedTheme(resolved);\r\n    };\r\n\r\n    mediaQuery.addEventListener(\"change\", handleChange);\r\n    return () => mediaQuery.removeEventListener(\"change\", handleChange);\r\n  }, [theme, mounted]);\r\n\r\n  // Don't render children until mounted to prevent hydration mismatch\r\n  if (!mounted) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <ThemeContext.Provider value={{ theme, setTheme: handleSetTheme, resolvedTheme }}>\r\n      {children}\r\n    </ThemeContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useTheme() {\r\n  const context = useContext(ThemeContext);\r\n  if (context === undefined) {\r\n    throw new Error(\"useTheme must be used within a ThemeProvider\");\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAYA,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAgC;AAEjE,oDAAoD;AACpD,SAAS;IACP,uCAAmC;;IAAa;IAChD,IAAI,CAAC,OAAO,UAAU,EAAE,OAAO,QAAQ,sDAAsD;IAC7F,OAAO,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;AAC9E;AAEA,wCAAwC;AACxC,SAAS,WAAW,KAAY;IAC9B,MAAM,OAAO,SAAS,eAAe;IACrC,MAAM,SAAS,UAAU,UAAW,UAAU,YAAY,qBAAqB;IAE/E,IAAI,QAAQ;QACV,KAAK,SAAS,CAAC,GAAG,CAAC;IACrB,OAAO;QACL,KAAK,SAAS,CAAC,MAAM,CAAC;IACxB;IAEA,OAAO,SAAS,SAAS;AAC3B;AAEO,SAAS,cAAc,EAAE,QAAQ,EAAiC;;IACvE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAC1C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,aAAa,AAAC,aAAa,OAAO,CAAC,YAAsB;YAC/D,SAAS;YAET,oDAAoD;YACpD,aAAa,OAAO,CAAC,SAAS;YAE9B,MAAM,WAAW,WAAW;YAC5B,iBAAiB;YACjB,WAAW;QACb;kCAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,iBAAiB,CAAC;QACtB,SAAS;QACT,aAAa,OAAO,CAAC,SAAS;QAE9B,MAAM,WAAW,WAAW;QAC5B,iBAAiB;IACnB;IAEA,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW,UAAU,UAAU;YAEpC,MAAM,aAAa,OAAO,UAAU,GAAG;YACvC,IAAI,CAAC,YAAY;YAEjB,MAAM;wDAAe;oBACnB,MAAM,WAAW,WAAW;oBAC5B,iBAAiB;gBACnB;;YAEA,WAAW,gBAAgB,CAAC,UAAU;YACtC;2CAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;kCAAG;QAAC;QAAO;KAAQ;IAEnB,oEAAoE;IACpE,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO,UAAU;YAAgB;QAAc;kBAC5E;;;;;;AAGP;GArDgB;KAAA;AAuDT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/layout/AvatarDropdown.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useRouter } from \"next/navigation\";\r\nimport Image from \"next/image\";\r\nimport {\r\n  UserIcon,\r\n  Cog6ToothIcon,\r\n  ArrowRightOnRectangleIcon,\r\n  SunIcon,\r\n  MoonIcon,\r\n  ComputerDesktopIcon,\r\n  BellIcon,\r\n  DocumentTextIcon,\r\n  ChartBarIcon,\r\n} from \"@heroicons/react/24/outline\";\r\nimport * as DropdownMenu from \"@radix-ui/react-dropdown-menu\";\r\nimport { useTheme } from \"../../_contexts/ThemeContext\";\r\nimport config from \"../../../public/data/config.json\";\r\n\r\ninterface User {\r\n  name: string;\r\n  email: string;\r\n  avatar?: string;\r\n  role: string;\r\n}\r\n\r\ninterface AvatarDropdownProps {\r\n  user: User;\r\n}\r\n\r\nexport function AvatarDropdown({ user }: AvatarDropdownProps) {\r\n  const router = useRouter();\r\n  const { theme, setTheme, resolvedTheme } = useTheme();\r\n\r\n  // Navigation handlers\r\n  const handleNavigation = (path: string) => {\r\n    router.push(path);\r\n  };\r\n\r\n  const getInitials = (name: string) => {\r\n    return name\r\n      .split(\" \")\r\n      .map((n) => n[0])\r\n      .join(\"\")\r\n      .toUpperCase()\r\n      .slice(0, 2);\r\n  };\r\n\r\n  const themeOptions = [\r\n    { value: \"light\", label: \"Claro\", icon: SunIcon },\r\n    { value: \"dark\", label: \"Oscuro\", icon: MoonIcon },\r\n    { value: \"system\", label: \"Sistema\", icon: ComputerDesktopIcon },\r\n  ];\r\n\r\n  const handleLogout = () => {\r\n    // In a real app, this would handle logout logic\r\n    console.log(\"Logging out...\");\r\n    // Clear any stored auth tokens\r\n    localStorage.removeItem(\"authToken\");\r\n    // Redirect to login page\r\n    window.location.href = \"/login\";\r\n  };\r\n\r\n  return (\r\n    <DropdownMenu.Root>\r\n      <DropdownMenu.Trigger asChild>\r\n        <button className=\"flex items-center cursor-pointer space-x-3 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900\">\r\n          {/* Avatar */}\r\n          <div className=\"relative\">\r\n            {user.avatar ? (\r\n              <Image\r\n                src={user.avatar}\r\n                alt={user.name}\r\n                width={32}\r\n                height={32}\r\n                className=\"w-8 h-8 rounded-full object-cover\"\r\n              />\r\n            ) : (\r\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-semibold\">\r\n                {getInitials(user.name)}\r\n              </div>\r\n            )}\r\n            {/* Online indicator */}\r\n            <div className=\"absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-900 rounded-full\"></div>\r\n          </div>\r\n\r\n          {/* User info */}\r\n          <div className=\"hidden md:block text-left\">\r\n            <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n              {user.name}\r\n            </div>\r\n            <div className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n              {user.role}\r\n            </div>\r\n          </div>\r\n        </button>\r\n      </DropdownMenu.Trigger>\r\n\r\n      <DropdownMenu.Portal>\r\n        <DropdownMenu.Content\r\n          className=\"min-w-56 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-1 z-50\"\r\n          sideOffset={5}\r\n          align=\"end\"\r\n        >\r\n          {/* User Info Header */}\r\n          <div className=\"px-3 py-2 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              {user.avatar ? (\r\n                <Image\r\n                  src={user.avatar}\r\n                  alt={user.name}\r\n                  width={40}\r\n                  height={40}\r\n                  className=\"w-10 h-10 rounded-full object-cover\"\r\n                />\r\n              ) : (\r\n                <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold\">\r\n                  {getInitials(user.name)}\r\n                </div>\r\n              )}\r\n              <div>\r\n                <div className=\"font-medium text-gray-900 dark:text-gray-100\">\r\n                  {user.name}\r\n                </div>\r\n                <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                  {user.email}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Menu Items */}\r\n          <div className=\"py-1\">\r\n            <DropdownMenu.Item\r\n              className=\"flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md cursor-pointer focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700\"\r\n              onClick={() => handleNavigation(\"/dashboard/profile\")}\r\n            >\r\n              <UserIcon className=\"h-4 w-4 mr-3\" />\r\n              Mi Perfil\r\n            </DropdownMenu.Item>\r\n\r\n            <DropdownMenu.Item className=\"flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md cursor-pointer focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700\">\r\n              <BellIcon className=\"h-4 w-4 mr-3\" />\r\n              Notificaciones\r\n            </DropdownMenu.Item>\r\n\r\n            <DropdownMenu.Item\r\n              className=\"flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md cursor-pointer focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700\"\r\n              onClick={() => handleNavigation(\"/dashboard/templates\")}\r\n            >\r\n              <DocumentTextIcon className=\"h-4 w-4 mr-3\" />\r\n              Plantillas\r\n            </DropdownMenu.Item>\r\n\r\n            {/* Analíticas - Solo mostrar si el feature flag está habilitado */}\r\n            {config.featureFlags.navigation.analyticsMenu.enabled && (\r\n              <DropdownMenu.Item\r\n                className=\"flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md cursor-pointer focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700\"\r\n                onClick={() => handleNavigation(\"/dashboard/analytics\")}\r\n              >\r\n                <ChartBarIcon className=\"h-4 w-4 mr-3\" />\r\n                Analíticas\r\n              </DropdownMenu.Item>\r\n            )}\r\n          </div>\r\n\r\n          <DropdownMenu.Separator className=\"h-px bg-gray-200 dark:bg-gray-700 my-1\" />\r\n\r\n          {/* Theme Selector */}\r\n          <DropdownMenu.Sub>\r\n            <DropdownMenu.SubTrigger\r\n              className=\"flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md cursor-pointer focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700\"\r\n              data-testid=\"theme-submenu-trigger\"\r\n            >\r\n              {resolvedTheme === \"dark\" ? (\r\n                <MoonIcon className=\"h-4 w-4 mr-3\" />\r\n              ) : (\r\n                <SunIcon className=\"h-4 w-4 mr-3\" />\r\n              )}\r\n              Tema\r\n              <div className=\"ml-auto text-xs text-gray-500 dark:text-gray-400\">\r\n                {theme === \"system\"\r\n                  ? \"Sistema\"\r\n                  : theme === \"dark\"\r\n                  ? \"Oscuro\"\r\n                  : \"Claro\"}\r\n              </div>\r\n            </DropdownMenu.SubTrigger>\r\n            <DropdownMenu.Portal>\r\n              <DropdownMenu.SubContent\r\n                className=\"min-w-32 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-1 z-50\"\r\n                sideOffset={2}\r\n                alignOffset={-5}\r\n              >\r\n                {themeOptions.map((option) => (\r\n                  <DropdownMenu.Item\r\n                    key={option.value}\r\n                    className=\"flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md cursor-pointer focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700\"\r\n                    onClick={() => {\r\n                      setTheme(option.value as \"light\" | \"dark\" | \"system\");\r\n                    }}\r\n                    data-testid={`theme-option-${option.value}`}\r\n                  >\r\n                    <option.icon className=\"h-4 w-4 mr-3\" />\r\n                    {option.label}\r\n                    {theme === option.value && (\r\n                      <div className=\"ml-auto w-2 h-2 bg-blue-500 rounded-full\"></div>\r\n                    )}\r\n                  </DropdownMenu.Item>\r\n                ))}\r\n              </DropdownMenu.SubContent>\r\n            </DropdownMenu.Portal>\r\n          </DropdownMenu.Sub>\r\n\r\n          <DropdownMenu.Item\r\n            className=\"flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md cursor-pointer focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700\"\r\n            onClick={() => handleNavigation(\"/dashboard/settings\")}\r\n          >\r\n            <Cog6ToothIcon className=\"h-4 w-4 mr-3\" />\r\n            Configuración\r\n          </DropdownMenu.Item>\r\n\r\n          <DropdownMenu.Separator className=\"h-px bg-gray-200 dark:bg-gray-700 my-1\" />\r\n\r\n          <DropdownMenu.Item\r\n            className=\"flex items-center px-3 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md cursor-pointer focus:outline-none focus:bg-red-50 dark:focus:bg-red-900/20\"\r\n            onClick={handleLogout}\r\n          >\r\n            <ArrowRightOnRectangleIcon className=\"h-4 w-4 mr-3\" />\r\n            Cerrar Sesión\r\n          </DropdownMenu.Item>\r\n        </DropdownMenu.Content>\r\n      </DropdownMenu.Portal>\r\n    </DropdownMenu.Root>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;;;AAjBA;;;;;;;AA8BO,SAAS,eAAe,EAAE,IAAI,EAAuB;;IAC1D,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,WAAQ,AAAD;IAElD,sBAAsB;IACtB,MAAM,mBAAmB,CAAC;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,EACf,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,MAAM,eAAe;QACnB;YAAE,OAAO;YAAS,OAAO;YAAS,MAAM,gNAAA,CAAA,UAAO;QAAC;QAChD;YAAE,OAAO;YAAQ,OAAO;YAAU,MAAM,kNAAA,CAAA,WAAQ;QAAC;QACjD;YAAE,OAAO;YAAU,OAAO;YAAW,MAAM,wOAAA,CAAA,sBAAmB;QAAC;KAChE;IAED,MAAM,eAAe;QACnB,gDAAgD;QAChD,QAAQ,GAAG,CAAC;QACZ,+BAA+B;QAC/B,aAAa,UAAU,CAAC;QACxB,yBAAyB;QACzB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,6LAAC,+KAAA,CAAA,OAAiB;;0BAChB,6LAAC,+KAAA,CAAA,UAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC;oBAAO,WAAU;;sCAEhB,6LAAC;4BAAI,WAAU;;gCACZ,KAAK,MAAM,iBACV,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAK,KAAK,MAAM;oCAChB,KAAK,KAAK,IAAI;oCACd,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;yDAGZ,6LAAC;oCAAI,WAAU;8CACZ,YAAY,KAAK,IAAI;;;;;;8CAI1B,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;0BAMlB,6LAAC,+KAAA,CAAA,SAAmB;0BAClB,cAAA,6LAAC,+KAAA,CAAA,UAAoB;oBACnB,WAAU;oBACV,YAAY;oBACZ,OAAM;;sCAGN,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;oCACZ,KAAK,MAAM,iBACV,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAK,KAAK,MAAM;wCAChB,KAAK,KAAK,IAAI;wCACd,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;6DAGZ,6LAAC;wCAAI,WAAU;kDACZ,YAAY,KAAK,IAAI;;;;;;kDAG1B,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;sCAOnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+KAAA,CAAA,OAAiB;oCAChB,WAAU;oCACV,SAAS,IAAM,iBAAiB;;sDAEhC,6LAAC,kNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAIvC,6LAAC,+KAAA,CAAA,OAAiB;oCAAC,WAAU;;sDAC3B,6LAAC,kNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAIvC,6LAAC,+KAAA,CAAA,OAAiB;oCAChB,WAAU;oCACV,SAAS,IAAM,iBAAiB;;sDAEhC,6LAAC,kOAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;gCAK9C,gGAAA,CAAA,UAAM,CAAC,YAAY,CAAC,UAAU,CAAC,aAAa,CAAC,OAAO,kBACnD,6LAAC,+KAAA,CAAA,OAAiB;oCAChB,WAAU;oCACV,SAAS,IAAM,iBAAiB;;sDAEhC,6LAAC,0NAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;sCAM/C,6LAAC,+KAAA,CAAA,YAAsB;4BAAC,WAAU;;;;;;sCAGlC,6LAAC,+KAAA,CAAA,MAAgB;;8CACf,6LAAC,+KAAA,CAAA,aAAuB;oCACtB,WAAU;oCACV,eAAY;;wCAEX,kBAAkB,uBACjB,6LAAC,kNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;iEAEpB,6LAAC,gNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCACnB;sDAEF,6LAAC;4CAAI,WAAU;sDACZ,UAAU,WACP,YACA,UAAU,SACV,WACA;;;;;;;;;;;;8CAGR,6LAAC,+KAAA,CAAA,SAAmB;8CAClB,cAAA,6LAAC,+KAAA,CAAA,aAAuB;wCACtB,WAAU;wCACV,YAAY;wCACZ,aAAa,CAAC;kDAEb,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC,+KAAA,CAAA,OAAiB;gDAEhB,WAAU;gDACV,SAAS;oDACP,SAAS,OAAO,KAAK;gDACvB;gDACA,eAAa,CAAC,aAAa,EAAE,OAAO,KAAK,EAAE;;kEAE3C,6LAAC,OAAO,IAAI;wDAAC,WAAU;;;;;;oDACtB,OAAO,KAAK;oDACZ,UAAU,OAAO,KAAK,kBACrB,6LAAC;wDAAI,WAAU;;;;;;;+CAVZ,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;sCAkB3B,6LAAC,+KAAA,CAAA,OAAiB;4BAChB,WAAU;4BACV,SAAS,IAAM,iBAAiB;;8CAEhC,6LAAC,4NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAI5C,6LAAC,+KAAA,CAAA,YAAsB;4BAAC,WAAU;;;;;;sCAElC,6LAAC,+KAAA,CAAA,OAAiB;4BAChB,WAAU;4BACV,SAAS;;8CAET,6LAAC,oPAAA,CAAA,4BAAyB;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAOlE;GA7MgB;;QACC,qIAAA,CAAA,YAAS;QACmB,oIAAA,CAAA,WAAQ;;;KAFrC", "debugId": null}}, {"offset": {"line": 1000, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_lib/useIsomorphicDate.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\n\r\n/**\r\n * Hook to handle date operations that need to be consistent between server and client\r\n * This prevents hydration mismatches by ensuring dates are only calculated on the client\r\n */\r\nexport function useIsomorphicDate() {\r\n  const [isClient, setIsClient] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  const now = isClient ? new Date() : new Date(0); // Use epoch time for SSR\r\n\r\n  return {\r\n    isClient,\r\n    now,\r\n    createDate: (dateString?: string | number | Date) => {\r\n      if (!isClient) {\r\n        // Return a consistent date for SSR\r\n        return new Date(0);\r\n      }\r\n      return dateString ? new Date(dateString) : new Date();\r\n    },\r\n  };\r\n}\r\n\r\n/**\r\n * Hook to determine if we're on the client side (after hydration)\r\n * Useful for preventing hydration mismatches\r\n */\r\nexport function useIsMounted() {\r\n  const [isMounted, setIsMounted] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n  }, []);\r\n\r\n  return isMounted;\r\n}\r\n\r\n/**\r\n * Safe hook for accessing localStorage that won't cause hydration issues\r\n */\r\nexport function useLocalStorage<T>(key: string, initialValue: T) {\r\n  const [storedValue, setStoredValue] = useState<T>(initialValue);\r\n  const [isLoaded, setIsLoaded] = useState(false);\r\n\r\n  useEffect(() => {\r\n    try {\r\n      const item = window.localStorage.getItem(key);\r\n      if (item) {\r\n        setStoredValue(JSON.parse(item));\r\n      }\r\n    } catch (error) {\r\n      console.warn(`Error reading localStorage key \"${key}\":`, error);\r\n    } finally {\r\n      setIsLoaded(true);\r\n    }\r\n  }, [key]);\r\n\r\n  const setValue = (value: T | ((val: T) => T)) => {\r\n    try {\r\n      const valueToStore =\r\n        value instanceof Function ? value(storedValue) : value;\r\n      setStoredValue(valueToStore);\r\n      if (typeof window !== \"undefined\") {\r\n        window.localStorage.setItem(key, JSON.stringify(valueToStore));\r\n      }\r\n    } catch (error) {\r\n      console.warn(`Error setting localStorage key \"${key}\":`, error);\r\n    }\r\n  };\r\n\r\n  return [storedValue, setValue, isLoaded] as const;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;;AAFA;;AAQO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,YAAY;QACd;sCAAG,EAAE;IAEL,MAAM,MAAM,WAAW,IAAI,SAAS,IAAI,KAAK,IAAI,yBAAyB;IAE1E,OAAO;QACL;QACA;QACA,YAAY,CAAC;YACX,IAAI,CAAC,UAAU;gBACb,mCAAmC;gBACnC,OAAO,IAAI,KAAK;YAClB;YACA,OAAO,aAAa,IAAI,KAAK,cAAc,IAAI;QACjD;IACF;AACF;GApBgB;AA0BT,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,aAAa;QACf;iCAAG,EAAE;IAEL,OAAO;AACT;IARgB;AAaT,SAAS,gBAAmB,GAAW,EAAE,YAAe;;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAK;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI;gBACF,MAAM,OAAO,OAAO,YAAY,CAAC,OAAO,CAAC;gBACzC,IAAI,MAAM;oBACR,eAAe,KAAK,KAAK,CAAC;gBAC5B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CAAC,CAAC,gCAAgC,EAAE,IAAI,EAAE,CAAC,EAAE;YAC3D,SAAU;gBACR,YAAY;YACd;QACF;oCAAG;QAAC;KAAI;IAER,MAAM,WAAW,CAAC;QAChB,IAAI;YACF,MAAM,eACJ,iBAAiB,WAAW,MAAM,eAAe;YACnD,eAAe;YACf,wCAAmC;gBACjC,OAAO,YAAY,CAAC,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;YAClD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,gCAAgC,EAAE,IAAI,EAAE,CAAC,EAAE;QAC3D;IACF;IAEA,OAAO;QAAC;QAAa;QAAU;KAAS;AAC1C;IA/BgB", "debugId": null}}, {"offset": {"line": 1089, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/layout/NotificationDropdown.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport * as DropdownMenu from \"@radix-ui/react-dropdown-menu\";\r\nimport { format } from \"date-fns\";\r\nimport { es } from \"date-fns/locale\";\r\nimport { BellIcon, CheckIcon, TrashIcon } from \"@heroicons/react/24/outline\";\r\nimport { BellIcon as BellSolidIcon } from \"@heroicons/react/24/solid\";\r\nimport { useIsMounted } from \"../../_lib/useIsomorphicDate\";\r\n\r\ninterface Notification {\r\n  id: string;\r\n  title: string;\r\n  message: string;\r\n  type: \"new_case\" | \"case_update\" | \"message\" | \"system\" | \"reminder\";\r\n  read: boolean;\r\n  createdAt: string;\r\n  caseId?: string;\r\n}\r\n\r\ninterface NotificationDropdownProps {\r\n  newCasesCount: number;\r\n}\r\n\r\nconst notificationTypeColors = {\r\n  new_case: \"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200\",\r\n  case_update:\r\n    \"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200\",\r\n  message:\r\n    \"bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200\",\r\n  system: \"bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200\",\r\n  reminder:\r\n    \"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200\",\r\n};\r\n\r\nconst notificationTypeLabels = {\r\n  new_case: \"Nuevo Caso\",\r\n  case_update: \"Actualización\",\r\n  message: \"Mensaje\",\r\n  system: \"Sistema\",\r\n  reminder: \"Recordatorio\",\r\n};\r\n\r\nexport function NotificationDropdown({\r\n  newCasesCount,\r\n}: NotificationDropdownProps) {\r\n  const [notifications, setNotifications] = useState<Notification[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [open, setOpen] = useState(false);\r\n  const isMounted = useIsMounted();\r\n\r\n  useEffect(() => {\r\n    const loadNotifications = async () => {\r\n      try {\r\n        // In a real app, this would come from an API\r\n        // For now, we'll generate mock notifications\r\n\r\n        const mockNotifications: Notification[] = [\r\n          {\r\n            id: \"notif-001\",\r\n            title: \"Nuevo caso asignado\",\r\n            message:\r\n              \"Se ha asignado el caso 'Despido sin causa – ACME S.A.' a tu cartera.\",\r\n            type: \"new_case\",\r\n            read: false,\r\n            createdAt: new Date(\"2024-01-15T10:00:00Z\").toISOString(), // 2 hours ago\r\n            caseId: \"c-001\",\r\n          },\r\n          {\r\n            id: \"notif-002\",\r\n            title: \"Mensaje sin leer\",\r\n            message:\r\n              \"María López ha enviado un nuevo mensaje sobre el caso de filtración.\",\r\n            type: \"message\",\r\n            read: false,\r\n            createdAt: new Date(\"2024-01-15T08:00:00Z\").toISOString(), // 4 hours ago\r\n            caseId: \"c-002\",\r\n          },\r\n          {\r\n            id: \"notif-003\",\r\n            title: \"Actualización de caso\",\r\n            message:\r\n              \"El caso de divorcio ha sido actualizado con nuevos documentos.\",\r\n            type: \"case_update\",\r\n            read: true,\r\n            createdAt: new Date(\"2024-01-14T12:00:00Z\").toISOString(), // 1 day ago\r\n            caseId: \"c-003\",\r\n          },\r\n          {\r\n            id: \"notif-004\",\r\n            title: \"Recordatorio de audiencia\",\r\n            message:\r\n              \"Tienes una audiencia programada para mañana a las 10:00 AM.\",\r\n            type: \"reminder\",\r\n            read: false,\r\n            createdAt: new Date(\"2024-01-15T06:00:00Z\").toISOString(), // 6 hours ago\r\n          },\r\n          {\r\n            id: \"notif-005\",\r\n            title: \"Actualización del sistema\",\r\n            message: \"El sistema se actualizará esta noche. Guarda tu trabajo.\",\r\n            type: \"system\",\r\n            read: true,\r\n            createdAt: new Date(\"2024-01-13T12:00:00Z\").toISOString(), // 2 days ago\r\n          },\r\n        ];\r\n\r\n        setNotifications(mockNotifications);\r\n      } catch (error) {\r\n        console.error(\"Error loading notifications:\", error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadNotifications();\r\n  }, []);\r\n\r\n  const unreadCount = notifications.filter((n) => !n.read).length;\r\n  // Use newCasesCount prop for badge display - only show badge when there are new cases\r\n  const badgeCount = newCasesCount;\r\n\r\n  const markAsRead = (notificationId: string) => {\r\n    setNotifications((prev) =>\r\n      prev.map((n) => (n.id === notificationId ? { ...n, read: true } : n))\r\n    );\r\n  };\r\n\r\n  const markAllAsRead = () => {\r\n    setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));\r\n  };\r\n\r\n  const deleteNotification = (notificationId: string) => {\r\n    setNotifications((prev) => prev.filter((n) => n.id !== notificationId));\r\n  };\r\n\r\n  const formatNotificationDate = (dateString: string) => {\r\n    // Only calculate relative time after hydration to prevent SSR mismatch\r\n    if (!isMounted) {\r\n      return \"Hace un momento\";\r\n    }\r\n\r\n    const date = new Date(dateString);\r\n    const now = new Date();\r\n    const diffInHours = Math.floor(\r\n      (now.getTime() - date.getTime()) / (1000 * 60 * 60)\r\n    );\r\n\r\n    if (diffInHours < 1) {\r\n      return \"Hace unos minutos\";\r\n    } else if (diffInHours < 24) {\r\n      return `Hace ${diffInHours} hora${diffInHours !== 1 ? \"s\" : \"\"}`;\r\n    } else {\r\n      return format(date, \"dd MMM yyyy - HH:mm\", { locale: es });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <DropdownMenu.Root open={open} onOpenChange={setOpen}>\r\n      <DropdownMenu.Trigger asChild>\r\n        <button\r\n          className=\"relative cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 mt-0.5 p-2 text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 rounded-md transition-colors\"\r\n          aria-label=\"Notificaciones\"\r\n        >\r\n          {badgeCount > 0 ? (\r\n            <BellSolidIcon className=\"h-6 w-6 text-blue-600 dark:text-blue-400\" />\r\n          ) : (\r\n            <BellIcon className=\"h-6 w-6\" />\r\n          )}\r\n          {badgeCount > 0 && (\r\n            <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs font-medium rounded-full flex items-center justify-center\">\r\n              {badgeCount > 9 ? \"9+\" : badgeCount}\r\n            </span>\r\n          )}\r\n        </button>\r\n      </DropdownMenu.Trigger>\r\n\r\n      <DropdownMenu.Portal>\r\n        <DropdownMenu.Content\r\n          className=\"min-w-80 max-w-96 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-0 z-50\"\r\n          sideOffset={5}\r\n          align=\"end\"\r\n        >\r\n          {/* Header */}\r\n          <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\r\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n              Notificaciones\r\n            </h3>\r\n            {unreadCount > 0 && (\r\n              <button\r\n                onClick={markAllAsRead}\r\n                className=\"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium cursor-pointer\"\r\n              >\r\n                Marcar todas como leídas\r\n              </button>\r\n            )}\r\n          </div>\r\n\r\n          {/* Content */}\r\n          <div className=\"max-h-96 overflow-y-auto\">\r\n            {loading ? (\r\n              <div className=\"flex justify-center items-center p-8\">\r\n                <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\r\n              </div>\r\n            ) : notifications.length === 0 ? (\r\n              <div className=\"text-center py-8 px-4\">\r\n                <BellIcon className=\"h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-3\" />\r\n                <p className=\"text-gray-500 dark:text-gray-400 text-sm\">\r\n                  No tienes notificaciones\r\n                </p>\r\n              </div>\r\n            ) : (\r\n              <div className=\"divide-y divide-gray-200 dark:divide-gray-700\">\r\n                {notifications.map((notification) => (\r\n                  <div\r\n                    key={notification.id}\r\n                    className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${\r\n                      !notification.read ? \"bg-blue-50 dark:bg-blue-900/10\" : \"\"\r\n                    }`}\r\n                  >\r\n                    <div className=\"flex items-start justify-between\">\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <div className=\"flex items-center space-x-2 mb-1\">\r\n                          <span\r\n                            className={`text-xs font-medium px-2 py-1 rounded-full ${\r\n                              notificationTypeColors[notification.type]\r\n                            }`}\r\n                          >\r\n                            {notificationTypeLabels[notification.type]}\r\n                          </span>\r\n                          {!notification.read && (\r\n                            <div className=\"w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full\"></div>\r\n                          )}\r\n                        </div>\r\n                        <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-1\">\r\n                          {notification.title}\r\n                        </h4>\r\n                        <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">\r\n                          {notification.message}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-500 dark:text-gray-500\">\r\n                          {formatNotificationDate(notification.createdAt)}\r\n                        </p>\r\n                      </div>\r\n                      <div className=\"flex items-center space-x-1 ml-2\">\r\n                        {!notification.read && (\r\n                          <button\r\n                            onClick={() => markAsRead(notification.id)}\r\n                            className=\"p-1 text-gray-400 dark:text-gray-500 hover:text-blue-600 dark:hover:text-blue-400 transition-colors cursor-pointer\"\r\n                            title=\"Marcar como leída\"\r\n                          >\r\n                            <CheckIcon className=\"h-4 w-4\" />\r\n                          </button>\r\n                        )}\r\n                        <button\r\n                          onClick={() => deleteNotification(notification.id)}\r\n                          className=\"p-1 text-gray-400 dark:text-gray-500 hover:text-red-600 dark:hover:text-red-400 transition-colors cursor-pointer\"\r\n                          title=\"Eliminar notificación\"\r\n                        >\r\n                          <TrashIcon className=\"h-4 w-4\" />\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Footer */}\r\n          {notifications.length > 0 && (\r\n            <div className=\"p-3 border-t border-gray-200 dark:border-gray-700\">\r\n              <button className=\"w-full text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium text-center cursor-pointer\">\r\n                Ver todas las notificaciones\r\n              </button>\r\n            </div>\r\n          )}\r\n        </DropdownMenu.Content>\r\n      </DropdownMenu.Portal>\r\n    </DropdownMenu.Root>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AARA;;;;;;;;AAwBA,MAAM,yBAAyB;IAC7B,UAAU;IACV,aACE;IACF,SACE;IACF,QAAQ;IACR,UACE;AACJ;AAEA,MAAM,yBAAyB;IAC7B,UAAU;IACV,aAAa;IACb,SAAS;IACT,QAAQ;IACR,UAAU;AACZ;AAEO,SAAS,qBAAqB,EACnC,aAAa,EACa;;IAC1B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM;oEAAoB;oBACxB,IAAI;wBACF,6CAA6C;wBAC7C,6CAA6C;wBAE7C,MAAM,oBAAoC;4BACxC;gCACE,IAAI;gCACJ,OAAO;gCACP,SACE;gCACF,MAAM;gCACN,MAAM;gCACN,WAAW,IAAI,KAAK,wBAAwB,WAAW;gCACvD,QAAQ;4BACV;4BACA;gCACE,IAAI;gCACJ,OAAO;gCACP,SACE;gCACF,MAAM;gCACN,MAAM;gCACN,WAAW,IAAI,KAAK,wBAAwB,WAAW;gCACvD,QAAQ;4BACV;4BACA;gCACE,IAAI;gCACJ,OAAO;gCACP,SACE;gCACF,MAAM;gCACN,MAAM;gCACN,WAAW,IAAI,KAAK,wBAAwB,WAAW;gCACvD,QAAQ;4BACV;4BACA;gCACE,IAAI;gCACJ,OAAO;gCACP,SACE;gCACF,MAAM;gCACN,MAAM;gCACN,WAAW,IAAI,KAAK,wBAAwB,WAAW;4BACzD;4BACA;gCACE,IAAI;gCACJ,OAAO;gCACP,SAAS;gCACT,MAAM;gCACN,MAAM;gCACN,WAAW,IAAI,KAAK,wBAAwB,WAAW;4BACzD;yBACD;wBAED,iBAAiB;oBACnB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,gCAAgC;oBAChD,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;yCAAG,EAAE;IAEL,MAAM,cAAc,cAAc,MAAM,CAAC,CAAC,IAAM,CAAC,EAAE,IAAI,EAAE,MAAM;IAC/D,sFAAsF;IACtF,MAAM,aAAa;IAEnB,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAC,OAChB,KAAK,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,iBAAiB;oBAAE,GAAG,CAAC;oBAAE,MAAM;gBAAK,IAAI;IAEtE;IAEA,MAAM,gBAAgB;QACpB,iBAAiB,CAAC,OAAS,KAAK,GAAG,CAAC,CAAC,IAAM,CAAC;oBAAE,GAAG,CAAC;oBAAE,MAAM;gBAAK,CAAC;IAClE;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;IACzD;IAEA,MAAM,yBAAyB,CAAC;QAC9B,uEAAuE;QACvE,IAAI,CAAC,WAAW;YACd,OAAO;QACT;QAEA,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,KAAK,KAAK,CAC5B,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAGpD,IAAI,cAAc,GAAG;YACnB,OAAO;QACT,OAAO,IAAI,cAAc,IAAI;YAC3B,OAAO,CAAC,KAAK,EAAE,YAAY,KAAK,EAAE,gBAAgB,IAAI,MAAM,IAAI;QAClE,OAAO;YACL,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,uBAAuB;gBAAE,QAAQ,8IAAA,CAAA,KAAE;YAAC;QAC1D;IACF;IAEA,qBACE,6LAAC,+KAAA,CAAA,OAAiB;QAAC,MAAM;QAAM,cAAc;;0BAC3C,6LAAC,+KAAA,CAAA,UAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC;oBACC,WAAU;oBACV,cAAW;;wBAEV,aAAa,kBACZ,6LAAC,gNAAA,CAAA,WAAa;4BAAC,WAAU;;;;;iDAEzB,6LAAC,kNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAErB,aAAa,mBACZ,6LAAC;4BAAK,WAAU;sCACb,aAAa,IAAI,OAAO;;;;;;;;;;;;;;;;;0BAMjC,6LAAC,+KAAA,CAAA,SAAmB;0BAClB,cAAA,6LAAC,+KAAA,CAAA,UAAoB;oBACnB,WAAU;oBACV,YAAY;oBACZ,OAAM;;sCAGN,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAyD;;;;;;gCAGtE,cAAc,mBACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;sCAOL,6LAAC;4BAAI,WAAU;sCACZ,wBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;uCAEf,cAAc,MAAM,KAAK,kBAC3B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,kNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;qDAK1D,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,6LAAC;wCAEC,WAAW,CAAC,8DAA8D,EACxE,CAAC,aAAa,IAAI,GAAG,mCAAmC,IACxD;kDAEF,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,WAAW,CAAC,2CAA2C,EACrD,sBAAsB,CAAC,aAAa,IAAI,CAAC,EACzC;8EAED,sBAAsB,CAAC,aAAa,IAAI,CAAC;;;;;;gEAE3C,CAAC,aAAa,IAAI,kBACjB,6LAAC;oEAAI,WAAU;;;;;;;;;;;;sEAGnB,6LAAC;4DAAG,WAAU;sEACX,aAAa,KAAK;;;;;;sEAErB,6LAAC;4DAAE,WAAU;sEACV,aAAa,OAAO;;;;;;sEAEvB,6LAAC;4DAAE,WAAU;sEACV,uBAAuB,aAAa,SAAS;;;;;;;;;;;;8DAGlD,6LAAC;oDAAI,WAAU;;wDACZ,CAAC,aAAa,IAAI,kBACjB,6LAAC;4DACC,SAAS,IAAM,WAAW,aAAa,EAAE;4DACzC,WAAU;4DACV,OAAM;sEAEN,cAAA,6LAAC,oNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;sEAGzB,6LAAC;4DACC,SAAS,IAAM,mBAAmB,aAAa,EAAE;4DACjD,WAAU;4DACV,OAAM;sEAEN,cAAA,6LAAC,oNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;uCA5CtB,aAAa,EAAE;;;;;;;;;;;;;;;wBAuD7B,cAAc,MAAM,GAAG,mBACtB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAO,WAAU;0CAAsI;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStK;GA9OgB;;QAMI,mIAAA,CAAA,eAAY;;;KANhB", "debugId": null}}, {"offset": {"line": 1519, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/layout/TopBar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { AvatarDropdown } from \"./AvatarDropdown\";\r\nimport { NotificationDropdown } from \"./NotificationDropdown\";\r\nimport { Bars3Icon } from \"@heroicons/react/24/outline\";\r\nimport { Logo } from \"../ui/Logo\";\r\n\r\n// Mock user data - in a real app this would come from auth context\r\nconst mockUser = {\r\n  name: \"Dr. <PERSON>\",\r\n  email: \"<EMAIL>\",\r\n  role: \"Abogada Senior\",\r\n  avatar: undefined, // Will use initials\r\n};\r\n\r\ninterface TopBarProps {\r\n  onMobileMenuToggle?: () => void;\r\n}\r\n\r\nexport function TopBar({ onMobileMenuToggle }: TopBarProps) {\r\n  const [newCasesCount, setNewCasesCount] = useState(0);\r\n  const router = useRouter();\r\n\r\n  const handleLogoClick = () => {\r\n    router.push(\"/dashboard\");\r\n  };\r\n\r\n  // Load new cases count from localStorage or calculate from cases data\r\n  useEffect(() => {\r\n    // In a real app, this would come from an API or context\r\n    // For now, we'll simulate getting new cases count\r\n    const loadNewCasesCount = async () => {\r\n      try {\r\n        const response = await fetch(\"/data/cases.json\");\r\n        const cases = await response.json();\r\n        const newCases = cases.filter(\r\n          (c: { status: string }) => c.status === \"new\"\r\n        );\r\n        setNewCasesCount(newCases.length);\r\n      } catch (error) {\r\n        console.error(\"Error loading cases:\", error);\r\n        setNewCasesCount(2); // Fallback\r\n      }\r\n    };\r\n\r\n    loadNewCasesCount();\r\n  }, []);\r\n\r\n  return (\r\n    <header className=\"relative z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-3 lg:px-6 py-4\">\r\n      <div className=\"flex items-center justify-between\">\r\n        {/* Left side - hamburger menu */}\r\n        <div className=\"flex items-center\">\r\n          {/* Hamburger menu button - solo visible en móvil/tablet */}\r\n          {onMobileMenuToggle && (\r\n            <button\r\n              onClick={onMobileMenuToggle}\r\n              className=\"lg:hidden p-2 cursor-pointer rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors\"\r\n              aria-label=\"Abrir menú\"\r\n            >\r\n              <Bars3Icon className=\"h-6 w-6 text-gray-600 dark:text-gray-400\" />\r\n            </button>\r\n          )}\r\n\r\n          {/* Page title - solo visible en desktop */}\r\n          <h2 className=\"hidden lg:block text-lg font-semibold text-gray-900 dark:text-gray-100\">\r\n            Dashboard\r\n          </h2>\r\n        </div>\r\n\r\n        {/* Center - Logo y título para móvil/tablet */}\r\n        <div\r\n          className=\"lg:hidden absolute cursor-pointer left-1/2 transform -translate-x-1/2 flex items-center space-x-3 hover:opacity-80 transition-opacity\"\r\n          onClick={handleLogoClick}\r\n        >\r\n          <Logo size={28} className=\"flex-shrink-0\" />\r\n          <h1 className=\"text-lg font-bold text-gray-900 dark:text-gray-100\">\r\n            X-Legal\r\n          </h1>\r\n        </div>\r\n\r\n        {/* Right side - notifications and avatar */}\r\n        <div className=\"flex items-center space-x-3\">\r\n          {/* Notifications */}\r\n          <NotificationDropdown newCasesCount={newCasesCount} />\r\n\r\n          {/* User Avatar Dropdown */}\r\n          <AvatarDropdown user={mockUser} />\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,mEAAmE;AACnE,MAAM,WAAW;IACf,MAAM;IACN,OAAO;IACP,MAAM;IACN,QAAQ;AACV;AAMO,SAAS,OAAO,EAAE,kBAAkB,EAAe;;IACxD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,kBAAkB;QACtB,OAAO,IAAI,CAAC;IACd;IAEA,sEAAsE;IACtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,wDAAwD;YACxD,kDAAkD;YAClD,MAAM;sDAAoB;oBACxB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,MAAM,QAAQ,MAAM,SAAS,IAAI;wBACjC,MAAM,WAAW,MAAM,MAAM;2EAC3B,CAAC,IAA0B,EAAE,MAAM,KAAK;;wBAE1C,iBAAiB,SAAS,MAAM;oBAClC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,wBAAwB;wBACtC,iBAAiB,IAAI,WAAW;oBAClC;gBACF;;YAEA;QACF;2BAAG,EAAE;IAEL,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;wBAEZ,oCACC,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,cAAW;sCAEX,cAAA,6LAAC,oNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAKzB,6LAAC;4BAAG,WAAU;sCAAyE;;;;;;;;;;;;8BAMzF,6LAAC;oBACC,WAAU;oBACV,SAAS;;sCAET,6LAAC,oIAAA,CAAA,OAAI;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCAC1B,6LAAC;4BAAG,WAAU;sCAAqD;;;;;;;;;;;;8BAMrE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,wJAAA,CAAA,uBAAoB;4BAAC,eAAe;;;;;;sCAGrC,6LAAC,kJAAA,CAAA,iBAAc;4BAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;AAKhC;GAzEgB;;QAEC,qIAAA,CAAA,YAAS;;;KAFV", "debugId": null}}, {"offset": {"line": 1691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/apps/abogados/dashboard/app/_components/layout/ClientLayout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { Sidebar } from \"./Sidebar\";\r\nimport { TopBar } from \"./TopBar\";\r\n\r\ninterface ClientLayoutProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport function ClientLayout({ children }: ClientLayoutProps) {\r\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\r\n\r\n  const toggleMobileMenu = () => {\r\n    setIsMobileMenuOpen(!isMobileMenuOpen);\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex h-screen\">\r\n      {/* Sidebar responsivo */}\r\n      <Sidebar\r\n        isMobileOpen={isMobileMenuOpen}\r\n        onMobileToggle={toggleMobileMenu}\r\n      />\r\n\r\n      {/* Contenido principal */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        <TopBar onMobileMenuToggle={toggleMobileMenu} />\r\n        <main className=\"flex-1 overflow-auto p-6 bg-gray-50 dark:bg-gray-900\">\r\n          {children}\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUO,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAC1D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,mBAAmB;QACvB,oBAAoB,CAAC;IACvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,2IAAA,CAAA,UAAO;gBACN,cAAc;gBACd,gBAAgB;;;;;;0BAIlB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,0IAAA,CAAA,SAAM;wBAAC,oBAAoB;;;;;;kCAC5B,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GAxBgB;KAAA", "debugId": null}}]}