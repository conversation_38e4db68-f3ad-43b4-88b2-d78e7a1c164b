"use client";

import { useState, useEffect } from "react";
import { CaseCard } from "./CaseCard";
import { Case } from "../../_lib/types";
import { ChevronLeftIcon, ChevronRightIcon, PlusIcon } from "@heroicons/react/24/outline";
import { AddCaseModal } from "../modals/AddCaseModal";

const columns = [
  { id: "new", title: "Nuevos", status: "new" as const },
  { id: "in_progress", title: "En Curso", status: "in_progress" as const },
  { id: "closed", title: "Cerrados", status: "closed" as const },
];



export function CaseList() {
  const [cases, setCases] = useState<Case[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeMobileTab, setActiveMobileTab] = useState(0); // Para navegación móvil
  const [showAddCaseModal, setShowAddCaseModal] = useState(false);

  // Load cases from JSON file and localStorage
  useEffect(() => {
    const loadCases = async () => {
      try {
        // Cargar casos desde JSON
        const response = await fetch("/data/cases.json");
        const casesData = await response.json();

        // Cargar casos adicionales desde sessionStorage
        const localCases = JSON.parse(sessionStorage.getItem("cases") || "[]");

        // Combinar casos (localStorage primero para que aparezcan arriba)
        const allCases = [...localCases, ...casesData];
        setCases(allCases);
      } catch (error) {
        console.error("Error loading cases:", error);
      } finally {
        setLoading(false);
      }
    };

    loadCases();
  }, []);

  const handleCaseAdded = (newCase: Case) => {
    // Agregar el nuevo caso al principio de la lista
    setCases(prev => [newCase, ...prev]);
    setShowAddCaseModal(false);
  };

  const getCasesByStatus = (status: string) => {
    return cases.filter((case_) => case_.status === status);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header con botón de agregar caso */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Gestión de Casos
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {cases.length} casos en total
          </p>
        </div>
        <button
          onClick={() => setShowAddCaseModal(true)}
          className="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900 transition-colors cursor-pointer"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Agregar Caso
        </button>
      </div>

      <div className="space-y-4">
        {/* Desktop: 3 columnas | Tablet: 2 columnas + scroll | Móvil: Tabs */}

        {/* Navegación móvil con tabs (solo visible en móvil) */}
        <div className="block sm:hidden mb-4">
          <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            {columns.map((column, index) => {
              const columnCases = getCasesByStatus(column.status);
              return (
                <button
                  key={column.id}
                  onClick={() => setActiveMobileTab(index)}
                  className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm font-medium transition-all ${
                    activeMobileTab === index
                      ? "bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm"
                      : "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
                  }`}
                >
                  <span>{column.title}</span>
                  <span className={`text-xs px-2 py-0.5 rounded-full ${
                    activeMobileTab === index
                      ? "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200"
                      : "bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300"
                  }`}>
                    {columnCases.length}
                  </span>
                </button>
              );
            })}
          </div>

          {/* Navegación con flechas */}
          <div className="flex justify-between items-center mt-3">
            <button
              onClick={() => setActiveMobileTab(Math.max(0, activeMobileTab - 1))}
              disabled={activeMobileTab === 0}
              className="flex items-center space-x-1 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeftIcon className="h-4 w-4" />
              <span>Anterior</span>
            </button>

            <div className="flex space-x-1">
              {columns.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full transition-all ${
                    activeMobileTab === index
                      ? "bg-blue-500 dark:bg-blue-400"
                      : "bg-gray-300 dark:bg-gray-600"
                  }`}
                />
              ))}
            </div>

            <button
              onClick={() => setActiveMobileTab(Math.min(columns.length - 1, activeMobileTab + 1))}
              disabled={activeMobileTab === columns.length - 1}
              className="flex items-center space-x-1 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span>Siguiente</span>
              <ChevronRightIcon className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Layout responsivo */}
        <div className="hidden sm:block">
          {/* Desktop: Grid de 3 columnas */}
          <div className="hidden lg:grid lg:grid-cols-3 lg:gap-6">
            {columns.map((column) => {
              const columnCases = getCasesByStatus(column.status);
              return (
                <div
                  key={column.id}
                  className="bg-white dark:bg-gray-800 rounded-xl border-0 shadow-sm hover:shadow-md transition-all duration-200 p-5"
                >
                  <div className="flex items-center justify-between mb-5">
                    <h3 className="font-bold text-gray-900 dark:text-gray-100 text-lg">
                      {column.title}
                    </h3>
                    <span className="bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-sm">
                      {columnCases.length}
                    </span>
                  </div>

                  <div className="space-y-4 min-h-[300px] p-3 rounded-xl bg-gray-50/50 dark:bg-gray-900/50">
                    {columnCases.map((case_) => (
                      <CaseCard key={case_.id} case={case_} />
                    ))}
                    {columnCases.length === 0 && (
                      <div className="flex flex-col items-center justify-center text-center text-gray-400 dark:text-gray-500 py-12 space-y-3">
                        <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                          <svg className="w-8 h-8 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <div>
                          <p className="font-medium">No hay casos</p>
                          <p className="text-xs">Los casos aparecerán aquí</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Tablet: Scroll horizontal */}
          <div className="lg:hidden overflow-x-auto pb-4">
            <div className="flex space-x-4 min-w-max">
              {columns.map((column) => {
                const columnCases = getCasesByStatus(column.status);
                return (
                  <div
                    key={column.id}
                    className="bg-white dark:bg-gray-800 rounded-xl border-0 shadow-sm hover:shadow-md transition-all duration-200 p-5 w-80 flex-shrink-0"
                  >
                    <div className="flex items-center justify-between mb-5">
                      <h3 className="font-bold text-gray-900 dark:text-gray-100 text-lg">
                        {column.title}
                      </h3>
                      <span className="bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-sm">
                        {columnCases.length}
                      </span>
                    </div>

                    <div className="space-y-4 min-h-[300px] p-3 rounded-xl bg-gray-50/50 dark:bg-gray-900/50">
                      {columnCases.map((case_) => (
                        <CaseCard key={case_.id} case={case_} />
                      ))}
                      {columnCases.length === 0 && (
                        <div className="flex flex-col items-center justify-center text-center text-gray-400 dark:text-gray-500 py-12 space-y-3">
                          <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                            <svg className="w-8 h-8 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                          </div>
                          <div>
                            <p className="font-medium">No hay casos</p>
                            <p className="text-xs">Los casos aparecerán aquí</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>


        {/* Vista móvil: Solo mostrar la columna activa */}
        <div className="block sm:hidden">
          {(() => {
            const activeColumn = columns[activeMobileTab];
            const columnCases = getCasesByStatus(activeColumn.status);

            return (
              <div className="bg-white dark:bg-gray-800 rounded-xl border-0 shadow-sm p-5">
                <div className="flex items-center justify-between mb-5">
                  <h3 className="font-bold text-gray-900 dark:text-gray-100 text-lg">
                    {activeColumn.title}
                  </h3>
                  <span className="bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-sm">
                    {columnCases.length}
                  </span>
                </div>

                <div className="space-y-4 min-h-[300px] p-3 rounded-xl bg-gray-50/50 dark:bg-gray-900/50">
                  {columnCases.map((case_) => (
                    <CaseCard key={case_.id} case={case_} />
                  ))}
                  {columnCases.length === 0 && (
                    <div className="flex flex-col items-center justify-center text-center text-gray-400 dark:text-gray-500 py-12 space-y-3">
                      <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                        <svg className="w-8 h-8 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <div>
                        <p className="font-medium">No hay casos</p>
                        <p className="text-xs">Los casos aparecerán aquí</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })()}
        </div>
      </div>

      {/* Modal para agregar caso */}
      <AddCaseModal
        isOpen={showAddCaseModal}
        onClose={() => setShowAddCaseModal(false)}
        onCaseAdded={handleCaseAdded}
      />
    </div>
  );
}
