"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import {
  FolderIcon,
  DocumentTextIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { Logo } from "../ui/Logo";
import config from "../../../public/data/config.json";

const allNavigation = [
  { name: "Casos", href: "/dashboard", icon: FolderIcon },
  { name: "Plantillas", href: "/dashboard/templates", icon: DocumentTextIcon },
  { name: "Analytics", href: "/dashboard/analytics", icon: ChartBarIcon },
  { name: "Ajustes", href: "/dashboard/settings", icon: Cog6ToothIcon },
];

// Filtrar navegación basado en feature flags - SOLO RENDERIZADO
const navigation = allNavigation.filter((item) => {
  if (item.name === "Analytics") {
    return config.featureFlags.navigation.analyticsMenu.enabled;
  }
  return true;
});

interface SidebarProps {
  isMobileOpen?: boolean;
  onMobileToggle?: () => void;
}

export function Sidebar({
  isMobileOpen = false,
  onMobileToggle,
}: SidebarProps = {}) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const pathname = usePathname();
  const router = useRouter();

  const handleLogoClick = () => {
    router.push("/dashboard");
    // Cerrar sidebar móvil si está abierto
    if (onMobileToggle && isMobileOpen) {
      onMobileToggle();
    }
  };

  // Load sidebar state from localStorage on mount (solo para desktop)
  useEffect(() => {
    const saved = localStorage.getItem("sidebar-collapsed");
    if (saved) {
      setIsCollapsed(JSON.parse(saved));
    }
  }, []);

  // Save sidebar state to localStorage (solo para desktop)
  const toggleSidebar = () => {
    const newState = !isCollapsed;
    setIsCollapsed(newState);
    localStorage.setItem("sidebar-collapsed", JSON.stringify(newState));
  };

  // Cerrar sidebar móvil al hacer clic en un enlace (solo en móvil/tablet)
  const handleLinkClick = () => {
    // Solo cerrar en móvil/tablet cuando el sidebar está abierto como overlay
    if (onMobileToggle && isMobileOpen) {
      onMobileToggle();
    }
  };

  return (
    <>
      {/* Overlay para móvil/tablet */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 bg-black opacity-50 z-[35] lg:hidden backdrop-blur-sm"
          onClick={onMobileToggle}
        />
      )}

      {/* Botón discreto para expandir sidebar - sale del borde */}
      <button
        onClick={toggleSidebar}
        className={`hidden lg:flex fixed top-20 cursor-pointer left-[79px] z-90 w-6 h-8 bg-white dark:bg-gray-800 border border-l-0 border-gray-200 dark:border-gray-700 rounded-r-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 items-center justify-center ${
          isCollapsed
            ? "opacity-100 translate-x-0 transition-all duration-500 delay-300"
            : "opacity-0 translate-x-[-100%] pointer-events-none transition-all duration-200"
        }`}
        aria-label="Expandir sidebar"
      >
        <ChevronRightIcon className="h-3 w-3 text-gray-500 dark:text-gray-400" />
      </button>

      {/* Sidebar */}
      <div
        className={`
          bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 transition-all duration-300

          ${
            isMobileOpen
              ? "fixed top-0 bottom-0 left-0 z-[45] w-64 flex flex-col lg:hidden"
              : "hidden lg:flex"
          }

          lg:flex-col lg:relative lg:inset-auto lg:z-auto
          ${isCollapsed ? "lg:w-20" : "lg:w-64"}
        `}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            {/* Logo y título */}
            <div
              className={`flex items-center cursor-pointer hover:opacity-80 transition-opacity ${
                isCollapsed ? "lg:justify-center lg:w-full" : "space-x-3"
              }`}
              onClick={handleLogoClick}
            >
              <Logo size={32} className="flex-shrink-0" />
              {/* En desktop: mostrar título solo si no está colapsado */}
              {/* En móvil: siempre mostrar título */}
              <h1
                className={`text-xl font-bold text-gray-900 dark:text-gray-100 ${
                  isCollapsed ? "lg:hidden" : "block"
                }`}
              >
                X-Legal
              </h1>
            </div>

            {/* Botones de control - solo mostrar cuando no está colapsado en desktop */}
            <div
              className={`flex items-center space-x-2 ${
                isCollapsed ? "lg:hidden" : ""
              }`}
            >
              {/* Botón cerrar para móvil */}
              <button
                onClick={onMobileToggle}
                className="lg:hidden p-1.5 cursor-pointer rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                aria-label="Cerrar menú"
              >
                <XMarkIcon className="h-5 w-5 text-gray-500 dark:text-gray-400" />
              </button>

              {/* Botón colapsar para desktop */}
              <button
                onClick={toggleSidebar}
                className="hidden lg:block p-1.5 cursor-pointer rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                aria-label="Colapsar sidebar"
              >
                <ChevronLeftIcon className="h-5 w-5 text-gray-500 dark:text-gray-400" />
              </button>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={handleLinkClick}
                  className={`flex items-center ${
                    isCollapsed ? "justify-center px-2 py-3" : "px-3 py-2"
                  } rounded-md text-sm font-medium transition-colors ${
                    isActive
                      ? "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400 border-r-2 border-blue-700 dark:border-blue-400"
                      : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100"
                  }`}
                  title={isCollapsed ? item.name : undefined}
                >
                  <item.icon
                    className={`${
                      isCollapsed ? "h-6 w-6 mx-auto" : "h-5 w-5 mr-3"
                    } ${
                      isActive
                        ? "text-blue-700 dark:text-blue-400"
                        : "text-gray-400 dark:text-gray-500"
                    }`}
                  />
                  {!isCollapsed && <span>{item.name}</span>}
                </Link>
              );
            })}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            {!isCollapsed && (
              <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
                © 2025 X-Legal SRL
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
