"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { XMarkIcon, CheckCircleIcon, ChevronDownIcon } from "@heroicons/react/24/outline";
import * as Dialog from "@radix-ui/react-dialog";
import { AvailableCase, CaseBid } from "../../_lib/types";

interface BidModalProps {
  case: AvailableCase;
  onClose: () => void;
  onSubmit: (bidData: Partial<CaseBid>) => void;
}

export function BidModal({ case: caseData, onClose, onSubmit }: BidModalProps) {
  const [step, setStep] = useState<"form" | "review" | "success">("form");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    feeType: "fixed",
    feeAmount: "",
    feeCurrency: "ARS",
    feePercentage: "",
    feeDescription: "",
    estimatedTimeline: "",
    experience: "",
    availability: "immediate",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setStep("review");
  };

  const handleConfirmSubmit = async () => {
    setIsSubmitting(true);

    // Simulate submission delay
    setTimeout(() => {
      const bidData = {
        proposedFee: {
          type: formData.feeType as "fixed" | "contingency" | "hourly",
          amount:
            formData.feeType !== "contingency"
              ? parseInt(formData.feeAmount)
              : undefined,
          currency:
            formData.feeType !== "contingency"
              ? (formData.feeCurrency as "ARS" | "USD" | "UMA")
              : undefined,
          percentage:
            formData.feeType === "contingency"
              ? parseInt(formData.feePercentage)
              : undefined,
          description: formData.feeDescription,
        },
        estimatedTimeline: formData.estimatedTimeline,
        experience: formData.experience,
        availability: formData.availability,
      };

      onSubmit(bidData);
      setIsSubmitting(false);
      setStep("success");

      // Auto-close after success
      setTimeout(() => {
        onClose();
      }, 2000);
    }, 1500);
  };

  const renderStepContent = () => {
    switch (step) {
      case "form":
        return (
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Case Summary */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                {caseData.title}
              </h4>
              <div className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                <p>
                  <strong>Tipo:</strong> {caseData.type}
                </p>
                <p>
                  <strong>Cliente:</strong> {caseData.clientName}
                </p>
                <p>
                  <strong>Ubicación:</strong> {caseData.clientLocation}
                </p>
                <p>
                  <strong>Presupuesto:</strong> {caseData.budgetRange}
                </p>
              </div>
            </div>

            {/* Fee Structure */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Estructura de Honorarios
              </label>
              <div className="space-y-3">
                <label className="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                  <input
                    type="radio"
                    name="feeType"
                    value="fixed"
                    checked={formData.feeType === "fixed"}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        feeType: e.target.value,
                      }))
                    }
                    className="text-blue-600 focus:ring-blue-500"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-gray-100">
                      Honorario Fijo
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      Monto total por el caso completo
                    </div>
                  </div>
                </label>

                <label className="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                  <input
                    type="radio"
                    name="feeType"
                    value="hourly"
                    checked={formData.feeType === "hourly"}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        feeType: e.target.value,
                      }))
                    }
                    className="text-blue-600 focus:ring-blue-500"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-gray-100">Por Hora</div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      Tarifa por hora trabajada
                    </div>
                  </div>
                </label>

                <label className="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                  <input
                    type="radio"
                    name="feeType"
                    value="contingency"
                    checked={formData.feeType === "contingency"}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        feeType: e.target.value,
                      }))
                    }
                    className="text-blue-600 focus:ring-blue-500"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-gray-100">Cuota Litis</div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      Porcentaje del resultado obtenido
                    </div>
                  </div>
                </label>
              </div>
            </div>

            {/* Fee Amount */}
            {formData.feeType !== "contingency" ? (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Monto {formData.feeType === "hourly" ? "por Hora" : "Total"}
                </label>
                <div className="flex space-x-2 items-center">
                  <div className="flex-1">
                    <input
                      type="number"
                      required
                      value={formData.feeAmount}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          feeAmount: e.target.value,
                        }))
                      }
                      className="w-full h-10 px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="150000"
                    />
                  </div>
                  <div className="w-24 relative">
                    <select
                      value={formData.feeCurrency}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          feeCurrency: e.target.value,
                        }))
                      }
                      className="w-full h-10 px-3 pr-8 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none cursor-pointer"
                    >
                      <option value="ARS">ARS</option>
                      <option value="USD">USD</option>
                      <option value="UMA">UMA</option>
                    </select>
                    <ChevronDownIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500 pointer-events-none" />
                  </div>
                </div>
              </div>
            ) : (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Porcentaje (%)
                </label>
                <input
                  type="number"
                  required
                  min="1"
                  max="50"
                  value={formData.feePercentage}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      feePercentage: e.target.value,
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="30"
                />
              </div>
            )}

            {/* Fee Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Descripción de Honorarios
              </label>
              <textarea
                required
                rows={3}
                value={formData.feeDescription}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    feeDescription: e.target.value,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Incluye todas las gestiones necesarias, presentaciones ante organismos, etc."
              />
            </div>

            {/* Timeline */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Tiempo Estimado de Resolución
              </label>
              <input
                type="text"
                required
                value={formData.estimatedTimeline}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    estimatedTimeline: e.target.value,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="3-6 meses"
              />
            </div>

            {/* Experience */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Experiencia Relevante
              </label>
              <textarea
                required
                rows={4}
                value={formData.experience}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    experience: e.target.value,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Describa su experiencia en casos similares, resultados obtenidos, especialización en el área, etc."
              />
            </div>

            {/* Availability */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Disponibilidad para Comenzar
              </label>
              <select
                value={formData.availability}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    availability: e.target.value,
                  }))
                }
                className="w-full cursor-pointer px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="immediate">Inmediata</option>
                <option value="1_week">En 1 semana</option>
                <option value="2_weeks">En 2 semanas</option>
                <option value="1_month">En 1 mes</option>
              </select>
            </div>

            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors cursor-pointer"
              >
                Cancelar
              </button>
              <button
                type="submit"
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors cursor-pointer"
              >
                Revisar Propuesta
              </button>
            </div>
          </form>
        );

      case "review":
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Revisar Propuesta
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Verifique los detalles antes de enviar su propuesta
              </p>
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="font-medium text-gray-900 dark:text-gray-100">Honorarios:</span>
                <span className="text-gray-600 dark:text-gray-300">
                  {formData.feeType === "contingency"
                    ? `${formData.feePercentage}% del resultado`
                    : `${formData.feeCurrency} ${parseInt(formData.feeAmount).toLocaleString(
                        "es-AR"
                      )} ${
                        formData.feeType === "hourly" ? "por hora" : "total"
                      }`}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium text-gray-900 dark:text-gray-100">
                  Tiempo estimado:
                </span>
                <span className="text-gray-600 dark:text-gray-300">
                  {formData.estimatedTimeline}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium text-gray-900 dark:text-gray-100">
                  Disponibilidad:
                </span>
                <span className="text-gray-600 dark:text-gray-300">
                  {formData.availability === "immediate"
                    ? "Inmediata"
                    : formData.availability === "1_week"
                    ? "En 1 semana"
                    : formData.availability === "2_weeks"
                    ? "En 2 semanas"
                    : "En 1 mes"}
                </span>
              </div>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4">
              <p className="text-sm text-blue-800 dark:text-blue-200">
                <strong>Nota:</strong> Una vez enviada, su propuesta será
                revisada por el cliente. Recibirá una notificación cuando el
                cliente tome una decisión.
              </p>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setStep("form")}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors cursor-pointer"
              >
                Editar
              </button>
              <button
                onClick={handleConfirmSubmit}
                disabled={isSubmitting}
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors cursor-pointer"
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Enviando...
                  </div>
                ) : (
                  "Enviar Propuesta"
                )}
              </button>
            </div>
          </div>
        );

      case "success":
        return (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
            className="text-center space-y-6"
          >
            <div className="text-green-600">
              <CheckCircleIcon className="h-16 w-16 mx-auto mb-4" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                ¡Propuesta Enviada!
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Su propuesta para &quot;{caseData.title}&quot; ha sido enviada
                exitosamente. El cliente la revisará y se pondrá en contacto con
                usted.
              </p>
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog.Root open onOpenChange={onClose}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-black/50 z-50" />
        <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-gray-800 rounded-lg shadow-xl z-50 w-full max-w-2xl p-6 max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between mb-6">
            <Dialog.Title className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {step === "form" && "Enviar Propuesta"}
              {step === "review" && "Revisar Propuesta"}
              {step === "success" && "Propuesta Enviada"}
            </Dialog.Title>
            {step !== "success" && (
              <Dialog.Close asChild>
                <button className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300">
                  <XMarkIcon className="h-5 w-5" />
                </button>
              </Dialog.Close>
            )}
          </div>

          {renderStepContent()}
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
