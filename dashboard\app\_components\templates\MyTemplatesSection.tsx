"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  ArrowDownTrayIcon,
  DocumentIcon,
  TrashIcon,
  PencilIcon,
  EyeIcon,
  ChartBarIcon,
} from "@heroicons/react/24/outline";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import * as Tabs from "@radix-ui/react-tabs";
import { Template, UserTemplate } from "../../_lib/types";

export function MyTemplatesSection() {
  const [userTemplates, setUserTemplates] = useState<UserTemplate[]>([]);
  const [allTemplates, setAllTemplates] = useState<Template[]>([]);
  const [activeTab, setActiveTab] = useState("purchased");

  useEffect(() => {
    // Load user's templates from localStorage
    const savedUserTemplates = JSON.parse(
      localStorage.getItem("userTemplates") || "[]"
    );
    setUserTemplates(savedUserTemplates);

    // Load all templates to get details
    const loadTemplates = async () => {
      try {
        const response = await fetch("/data/templates.json");
        const templatesData = await response.json();
        setAllTemplates(templatesData);
      } catch (error) {
        console.error("Error loading templates:", error);
      }
    };

    loadTemplates();
  }, []);

  const purchasedTemplates = userTemplates
    .map((ut) => {
      const template = allTemplates.find((t) => t.id === ut.templateId);
      return template ? { ...template, userTemplate: ut } : null;
    })
    .filter(Boolean) as (Template & { userTemplate: UserTemplate })[];

  const uploadedTemplates = allTemplates.filter(
    (t) => t.authorId === "current-user"
  );

  const handleDownload = (template: Template) => {
    // Simulate download
    console.log("Downloading template:", template.title);

    // Update download count
    const updatedUserTemplates = userTemplates.map((ut) =>
      ut.templateId === template.id
        ? { ...ut, downloadCount: ut.downloadCount + 1 }
        : ut
    );
    setUserTemplates(updatedUserTemplates);
    localStorage.setItem("userTemplates", JSON.stringify(updatedUserTemplates));
  };

  const handleDelete = (templateId: string) => {
    const updatedUserTemplates = userTemplates.filter(
      (ut) => ut.templateId !== templateId
    );
    setUserTemplates(updatedUserTemplates);
    localStorage.setItem("userTemplates", JSON.stringify(updatedUserTemplates));
  };

  return (
    <div className="space-y-6">
      <Tabs.Root value={activeTab} onValueChange={setActiveTab}>
        <Tabs.List className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          <Tabs.Trigger
            value="purchased"
            className="flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
          >
            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
            Compradas ({purchasedTemplates.length})
          </Tabs.Trigger>
          <Tabs.Trigger
            value="uploaded"
            className="flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-gray-100 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700 data-[state=active]:text-blue-600 dark:data-[state=active]:text-blue-400 data-[state=active]:shadow-sm data-[state=active]:hover:bg-white dark:data-[state=active]:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
          >
            <DocumentIcon className="h-4 w-4 mr-2" />
            Subidas ({uploadedTemplates.length})
          </Tabs.Trigger>
        </Tabs.List>

        <div className="mt-6">
          <Tabs.Content value="purchased">
            {purchasedTemplates.length > 0 ? (
              <div className="space-y-4">
                {purchasedTemplates.map((template, index) => (
                  <motion.div
                    key={template.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                            {template.title}
                          </h3>
                          <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-full">
                            {template.legalArea}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                          {template.description}
                        </p>
                        <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                          <span>
                            Comprado:{" "}
                            {format(
                              new Date(template.userTemplate.purchasedAt),
                              "dd MMM yyyy",
                              { locale: es }
                            )}
                          </span>
                          <span>
                            Descargas: {template.userTemplate.downloadCount}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => handleDownload(template)}
                          className="p-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md transition-colors cursor-pointer"
                          title="Descargar"
                        >
                          <ArrowDownTrayIcon className="h-4 w-4" />
                        </button>
                        <button
                          className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md transition-colors cursor-pointer"
                          title="Vista previa"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(template.id)}
                          className="p-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-colors cursor-pointer"
                          title="Eliminar de biblioteca"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <ArrowDownTrayIcon className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400 mb-2">
                  No tienes plantillas compradas
                </p>
                <p className="text-sm text-gray-400 dark:text-gray-500">
                  Explora el marketplace para encontrar plantillas útiles para
                  tu práctica
                </p>
              </div>
            )}
          </Tabs.Content>

          <Tabs.Content value="uploaded">
            {uploadedTemplates.length > 0 ? (
              <div className="space-y-4">
                {uploadedTemplates.map((template, index) => (
                  <motion.div
                    key={template.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                            {template.title}
                          </h3>
                          <span
                            className={`text-xs px-2 py-1 rounded-full ${
                              template.status === "published"
                                ? "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200"
                                : template.status === "under_review"
                                ? "bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200"
                                : "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                            }`}
                          >
                            {template.status === "published" && "Publicado"}
                            {template.status === "under_review" &&
                              "En Revisión"}
                            {template.status === "draft" && "Borrador"}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                          {template.description}
                        </p>
                        <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                          <span>
                            Creado:{" "}
                            {format(
                              new Date(template.createdAt),
                              "dd MMM yyyy",
                              { locale: es }
                            )}
                          </span>
                          <span>Descargas: {template.downloadCount}</span>
                          <span>
                            Rating:{" "}
                            {template.rating > 0
                              ? template.rating.toFixed(1)
                              : "Sin calificar"}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          className="p-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md transition-colors cursor-pointer"
                          title="Ver estadísticas"
                        >
                          <ChartBarIcon className="h-4 w-4" />
                        </button>
                        <button
                          className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md transition-colors cursor-pointer"
                          title="Editar"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          className="p-2 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md transition-colors cursor-pointer"
                          title="Vista previa"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>

                    {/* Analytics for published templates */}
                    {template.status === "published" &&
                      template.downloadCount > 0 && (
                        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                          <div className="grid grid-cols-3 gap-4 text-center">
                            <div>
                              <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {template.downloadCount}
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                Descargas
                              </div>
                            </div>
                            <div>
                              <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                ARS{" "}
                                {(
                                  template.price * template.downloadCount
                                ).toLocaleString("es-AR")}
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                Ingresos
                              </div>
                            </div>
                            <div>
                              <div className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                {template.rating > 0
                                  ? template.rating.toFixed(1)
                                  : "-"}
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                Rating
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <DocumentIcon className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400 mb-2">No has subido plantillas</p>
                <p className="text-sm text-gray-400 dark:text-gray-500">
                  Comparte tus plantillas con otros abogados y genera ingresos
                  adicionales
                </p>
              </div>
            )}
          </Tabs.Content>
        </div>
      </Tabs.Root>
    </div>
  );
}
