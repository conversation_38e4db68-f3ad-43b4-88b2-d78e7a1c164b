"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AvatarDropdown } from "./AvatarDropdown";
import { NotificationDropdown } from "./NotificationDropdown";
import { Bars3Icon } from "@heroicons/react/24/outline";
import { Logo } from "../ui/Logo";

// Mock user data - in a real app this would come from auth context
const mockUser = {
  name: "Dr. <PERSON>",
  email: "<EMAIL>",
  role: "Abogada Senior",
  avatar: undefined, // Will use initials
};

interface TopBarProps {
  onMobileMenuToggle?: () => void;
}

export function TopBar({ onMobileMenuToggle }: TopBarProps) {
  const [newCasesCount, setNewCasesCount] = useState(0);
  const router = useRouter();

  const handleLogoClick = () => {
    router.push("/dashboard");
  };

  // Load new cases count from localStorage or calculate from cases data
  useEffect(() => {
    // In a real app, this would come from an API or context
    // For now, we'll simulate getting new cases count
    const loadNewCasesCount = async () => {
      try {
        const response = await fetch("/data/cases.json");
        const cases = await response.json();
        const newCases = cases.filter(
          (c: { status: string }) => c.status === "new"
        );
        setNewCasesCount(newCases.length);
      } catch (error) {
        console.error("Error loading cases:", error);
        setNewCasesCount(2); // Fallback
      }
    };

    loadNewCasesCount();
  }, []);

  return (
    <header className="relative z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-3 lg:px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left side - hamburger menu */}
        <div className="flex items-center">
          {/* Hamburger menu button - solo visible en móvil/tablet */}
          {onMobileMenuToggle && (
            <button
              onClick={onMobileMenuToggle}
              className="lg:hidden p-2 cursor-pointer rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              aria-label="Abrir menú"
            >
              <Bars3Icon className="h-6 w-6 text-gray-600 dark:text-gray-400" />
            </button>
          )}

          {/* Page title - solo visible en desktop */}
          <h2 className="hidden lg:block text-lg font-semibold text-gray-900 dark:text-gray-100">
            Dashboard
          </h2>
        </div>

        {/* Center - Logo y título para móvil/tablet */}
        <div
          className="lg:hidden absolute cursor-pointer left-1/2 transform -translate-x-1/2 flex items-center space-x-3 hover:opacity-80 transition-opacity"
          onClick={handleLogoClick}
        >
          <Logo size={28} className="flex-shrink-0" />
          <h1 className="text-lg font-bold text-gray-900 dark:text-gray-100">
            X-Legal
          </h1>
        </div>

        {/* Right side - notifications and avatar */}
        <div className="flex items-center space-x-3">
          {/* Notifications */}
          <NotificationDropdown newCasesCount={newCasesCount} />

          {/* User Avatar Dropdown */}
          <AvatarDropdown user={mockUser} />
        </div>
      </div>
    </header>
  );
}
